# System Architecture

<cite>
**Referenced Files in This Document**   
- [README.md](file://README.md)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)
- [backend/app/services/monitoring/data_gatherer.py](file://backend/app/services/monitoring/data_gatherer.py)
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/api/recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [backend/app/api/print.py](file://backend/app/api/print.py)
- [backend/app/api/configuration.py](file://backend/app/api/configuration.py)
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py)
- [backend/app/services/job_management/mixins/layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/config/job_config.py](file://backend/app/config/job_config.py)
- [opcua_simulator.py](file://opcua_simulator.py)
- [frontend/vite.config.js](file://frontend/vite.config.js)
- [backend/app/websockets.py](file://backend/app/websockets.py)
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect job management architecture refactoring with consolidated mixins
- Enhanced dependency injection section with singleton pattern implementation for WebSocket services and status poller
- Updated architecture overview to reflect modular job management structure
- Added detailed analysis of MultiMaterialJobService and its mixin composition
- Updated dependency analysis to reflect new initialization flow with singleton services
- Removed references to deprecated coordination_engine and multilayer_job_manager modules
- Updated service layer abstraction section to reflect new job management service structure
- Added new diagram for MultiMaterialJobService mixin composition
- Updated performance considerations to reflect singleton pattern benefits

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The APIRecoater_Ethernet system is a modern web-based Human-Machine Interface (HMI) designed for the Aerosint SPD Recoater system. It replaces the default SwaggerUI with an intuitive, responsive interface suitable for industrial and laboratory environments. The system follows a client-server architecture with a FastAPI backend and Vue.js frontend, communicating via REST APIs and WebSockets for real-time status updates. It integrates with industrial devices through OPC UA protocol for hardware control and coordination. The application enables full control of recoater functions including axis motion, drum/hopper/leveler management, and multi-material print job execution. Recent updates have enhanced the job management architecture with consolidated mixins and clearer separation of concerns. The WebSocket services and status poller have been converted to singleton pattern to resolve circular dependencies. The coordination_engine and multilayer_job_manager have been split into sub-modules for better organization. These changes improve code maintainability, reduce coupling, and enhance system reliability.

## Project Structure
The project is organized into two main directories: `backend` and `frontend`, following a clear separation of concerns between server-side logic and client-side presentation.

```mermaid
graph TB
subgraph "Root"
README[README.md]
AGENT[AGENT.md]
config[config.py]
run[run.bat]
debug[debug_run.bat]
install[install_deps.bat]
opcua_sim[opcua_simulator.py]
simple_conn[simple_connection_test.py]
test_opcua[test_opcua_client.py]
end
subgraph "Backend"
backend[backend/]
app[app/]
services[backend/services/]
tests[backend/tests/]
requirements[backend/requirements.txt]
subgraph "app"
api[api/]
config[config/]
models[models/]
services[app/services/]
utils[utils/]
dependencies[dependencies.py]
main[main.py]
websockets[websockets.py]
end
end
subgraph "Frontend"
frontend[frontend/]
src[src/]
package[package.json]
vite[vite.config.js]
subgraph "src"
components[components/]
router[router/]
services[services/]
stores[stores/]
views[views/]
App[App.vue]
main[main.js]
style[style.css]
end
end
README --> backend
README --> frontend
backend --> app
app --> api
app --> config
app --> models
app --> services
app --> utils
app --> dependencies
app --> main
app --> websockets
backend --> services
backend --> tests
backend --> requirements
frontend --> src
frontend --> package
frontend --> vite
src --> components
src --> router
src --> services
src --> stores
src --> views
src --> App
src --> main
src --> style
```

**Diagram sources**
- [README.md](file://README.md)
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)

**Section sources**
- [README.md](file://README.md)

## Core Components
The system's core functionality is distributed across several key components that handle communication, state management, hardware integration, and business logic.

**Section sources**
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Architecture Overview
The APIRecoater_Ethernet system follows a client-server architecture with a clear separation between frontend presentation and backend services. The backend, built with FastAPI, exposes RESTful APIs and WebSocket endpoints for real-time communication. The frontend, implemented with Vue.js 3, provides a responsive user interface that interacts with the backend through these APIs.

```mermaid
graph TB
subgraph "Frontend"
UI[User Interface]
Store[Pinia Store]
API[API Service]
end
subgraph "Backend"
FastAPI[FastAPI Server]
WebSockets[WebSocket Handler]
WebSocketManager[WebSocket Manager]
StatusPoller[Status Poller]
OPCUAService[OPC UA Service]
JobService[MultiMaterialJobService]
RecoaterClient[Recoater Client]
end
subgraph "Hardware"
OPCUAServer[OPC UA Server]
PLC[TwinCAT PLC]
Recoater[Recoater Device]
end
UI --> API
API --> FastAPI
FastAPI --> WebSockets
WebSockets --> WebSocketManager
FastAPI --> StatusPoller
FastAPI --> OPCUAService
FastAPI --> JobService
StatusPoller --> WebSocketManager
OPCUAService --> OPCUAServer
JobService --> RecoaterClient
RecoaterClient --> Recoater
OPCUAServer --> PLC
style UI fill:#f9f,stroke:#333
style FastAPI fill:#bbf,stroke:#333
style OPCUAServer fill:#f96,stroke:#333
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [frontend/src/main.js](file://frontend/src/main.js)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Detailed Component Analysis

### Backend Application Structure
The backend application is structured around FastAPI's modular router system, with distinct API endpoints for different functional areas.

```mermaid
classDiagram
class FastAPI {
+title : str
+description : str
+version : str
+lifespan : function
}
class APIRouter {
+prefix : str
+tags : List[str]
}
class StatusRouter {
+get_status()
+health_check()
+set_server_state()
}
class AxisRouter {
+get_axis_status()
+set_axis_position()
}
class RecoaterRouter {
+get_drum_motion()
+set_drum_motion()
+get_drum_ejection()
+set_drum_ejection()
}
class PrintRouter {
+get_layer_parameters()
+set_layer_parameters()
+start_print_job()
+cancel_print_job()
}
class ConfigRouter {
+get_configuration()
+set_configuration()
}
FastAPI --> APIRouter : "includes"
APIRouter <|-- StatusRouter
APIRouter <|-- AxisRouter
APIRouter <|-- RecoaterRouter
APIRouter <|-- PrintRouter
APIRouter <|-- ConfigRouter
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/api/axis.py](file://backend/app/api/axis.py)
- [backend/app/api/recoater_controls.py](file://backend/app/api/recoater_controls.py)
- [backend/app/api/print.py](file://backend/app/api/print.py)
- [backend/app/api/configuration.py](file://backend/app/api/configuration.py)

**Section sources**
- [backend/app/main.py](file://backend/app/main.py)

### Frontend Application Structure
The frontend application follows a component-based architecture with Vue.js 3 and Pinia for state management.

```mermaid
classDiagram
class VueApp {
+createApp()
+use(router)
+use(pinia)
+mount()
}
class Router {
+routes : Array
+navigation guards
}
class Pinia {
+state
+getters
+actions
}
class PrintJobStore {
-jobStatus : Object
-layerParameters : Object
-drumStates : Array
+updateJobStatus()
+setLayerParameters()
+updateDrumState()
}
class StatusStore {
-systemStatus : Object
-axisData : Object
-drumData : Array
-levelerData : Object
+updateStatus()
+subscribeToUpdates()
}
class ApiService {
+get(endpoint)
+post(endpoint, data)
+delete(endpoint)
+websocketConnect()
}
VueApp --> Router
VueApp --> Pinia
Pinia --> PrintJobStore
Pinia --> StatusStore
PrintJobStore --> ApiService
StatusStore --> ApiService
ApiService --> FastAPI : "HTTP/WebSocket"
class View {
+renders
+dispatches actions
}
class Component {
+reusable UI
+emits events
}
View <|-- ConfigurationView
View <|-- PrintView
View <|-- RecoaterView
View <|-- StatusView
View <|-- AxisView
Component <|-- DrumControl
Component <|-- HopperControl
Component <|-- LevelerControl
Component <|-- MultiLayerJobControl
Component <|-- StatusIndicator
Component <|-- FileUploadColumn
Component <|-- CriticalErrorModal
Component <|-- JobProgressDisplay
Component <|-- Legend
View --> Component
View --> Store
```

**Diagram sources**
- [frontend/src/main.js](file://frontend/src/main.js)
- [frontend/src/router/index.js](file://frontend/src/router/index.js)
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/main.js](file://frontend/src/main.js)

### WebSocket Architecture Refactoring
The WebSocket implementation has been refactored by extracting the WebSocket functionality from main.py into a dedicated websockets.py module. This improves code organization and separation of concerns by isolating WebSocket handling logic.

```
classDiagram
class WebSocketHandler {
-websocket_manager : WebSocketConnectionManager
+websocket_endpoint(websocket: WebSocket)
+handle_websocket_message(websocket: WebSocket, message_text: str)
}
class WebSocketConnectionManager {
+active_connections : List[WebSocket]
+connection_subscriptions : Dict[WebSocket, Set[str]]
+connect(websocket: WebSocket)
+disconnect(websocket: WebSocket)
+update_subscription(websocket: WebSocket, data_types: List[str])
+broadcast(message: Dict[str, Any])
+_filter_message_for_connection(websocket: WebSocket, message: Dict[str, Any])
+get_required_data_types() : Set[str]
}
class StatusPollingService {
-websocket_manager : WebSocketConnectionManager
+poll_interval : float
+start()
+stop()
+_polling_loop()
+_poll_and_broadcast()
}
WebSocketHandler --> WebSocketConnectionManager : "uses"
StatusPollingService --> WebSocketConnectionManager : "uses"
```

**Diagram sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)

**Section sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/main.py](file://backend/app/main.py)

### WebSocket Handler Implementation
The WebSocketHandler class encapsulates the WebSocket endpoint logic and message processing, providing a clean interface for handling real-time communication.

```
sequenceDiagram
participant Frontend as "Vue.js Frontend"
participant WebSocket as "WebSocket Connection"
participant Handler as "WebSocketHandler"
participant Manager as "WebSocketConnectionManager"
participant Poller as "StatusPoller"
participant Backend as "Backend Application"
Frontend->>WebSocket: Connect to /ws
WebSocket->>Handler: websocket_endpoint()
Handler->>Manager: connect(websocket)
Manager->>Manager: Add to active_connections
Manager->>Manager: Initialize subscription (status only)
Handler->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription() on subscribe message
Manager->>Manager: Update connection_subscriptions
loop Every poll_interval seconds
Poller->>Poller: _polling_loop()
Poller->>Manager: get_required_data_types()
Manager->>Poller: Return subscribed data types
Poller->>Manager: broadcast(message)
Manager->>Manager: _filter_message_for_connection()
Manager->>WebSocket: send_json(filtered_message)
WebSocket->>Frontend: Receive status update
end
Frontend->>WebSocket: Send subscription update
WebSocket->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription(websocket, data_types)
```

**Diagram sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)

**Section sources**
- [backend/app/websockets.py](file://backend/app/websockets.py)

### OPC UA Integration Architecture
The system uses OPC UA for industrial communication between the backend and the TwinCAT PLC, enabling coordination of multi-material print jobs. The OPC UA service implementation includes enhanced error handling with auto-restart capabilities and configurable parameters through environment variables.

```
sequenceDiagram
participant Backend as "Backend Application"
participant OPCUAService as "OPC UA Service"
participant ServerMgr as "OPCUAServerManager"
participant OPCUA as "asyncua.Server"
participant PLC as "TwinCAT PLC"
Backend->>OPCUAService: set_job_active(job_id, layers)
OPCUAService->>OPCUAService: Validate parameters
OPCUAService->>ServerMgr: write_variable("job_active", True)
OPCUAService->>ServerMgr: write_variable("job_id", job_id)
OPCUAService->>ServerMgr: write_variable("total_layers", layers)
OPCUAService->>ServerMgr: write_variable("current_layer", 0)
OPCUAService->>ServerMgr: write_variable("coord_status", "active")
ServerMgr->>OPCUA: Update variable values
OPCUA->>PLC: Notify variable changes
PLC->>OPCUA: Read variables
OPCUA->>ServerMgr: Variable read events
ServerMgr->>OPCUAService: Return operation result
OPCUAService->>Backend: Return success
Backend->>OPCUAService: set_drums_ready(states)
OPCUAService->>OPCUAService: Process drum states
OPCUAService->>ServerMgr: write_variable("drum0_ready", states[0])
OPCUAService->>ServerMgr: write_variable("drum1_ready", states[1])
OPCUAService->>ServerMgr: write_variable("drum2_ready", states[2])
OPCUAService->>ServerMgr: write_variable("all_drums_rdy", all(states))
ServerMgr->>OPCUA: Update variables
OPCUA->>PLC: Notify changes
PLC->>OPCUA: Read variables
OPCUA->>ServerMgr: Read events
ServerMgr->>OPCUAService: Return result
OPCUAService->>Backend: Return success
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

**Section sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

### Event-Driven Real-Time Updates
The system implements an event-driven architecture using WebSockets to provide real-time status updates to the frontend.

```
sequenceDiagram
participant Frontend as "Vue.js Frontend"
participant WebSocket as "WebSocket Connection"
participant Handler as "WebSocketHandler"
participant Manager as "WebSocketConnectionManager"
participant Poller as "StatusPoller"
participant Gatherer as "DataGatherer"
participant Client as "RecoaterClient"
participant Hardware as "Recoater Hardware"
Frontend->>WebSocket: Connect to /ws
WebSocket->>Handler: websocket_endpoint()
Handler->>Manager: connect(websocket)
Manager->>Manager: Add to active_connections
Manager->>Manager: Initialize subscription (status only)
loop Every poll_interval seconds
Poller->>Poller: _polling_loop()
Poller->>Manager: get_required_data_types()
Manager->>Poller: Return subscribed data types
Poller->>Gatherer: gather_all_data(client, required_types)
Gatherer->>Client: get_state()
Gatherer->>Client: get_drums() + get_drum_motion() etc.
Gatherer->>Client: get_leveler_pressure() + get_leveler_sensor()
Gatherer->>Client: get_layer_parameters() + get_print_job_status()
Client->>Hardware: HTTP API calls
Hardware->>Client: Return status data
Client->>Gatherer: Return data
Gatherer->>Poller: Return gathered_data
Poller->>Gatherer: construct_status_message(gathered_data)
Gatherer->>Poller: Return message
Poller->>Manager: broadcast(message)
Manager->>Manager: _filter_message_for_connection()
Manager->>WebSocket: send_json(filtered_message)
WebSocket->>Frontend: Receive status update
end
Frontend->>WebSocket: Send subscription update
WebSocket->>Handler: handle_websocket_message()
Handler->>Manager: update_subscription(websocket, data_types)
```

**Diagram sources**
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)
- [backend/app/services/monitoring/data_gatherer.py](file://backend/app/services/monitoring/data_gatherer.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

**Section sources**
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

### Service Layer Abstraction and Dependency Injection
The system implements service layer abstraction and dependency injection to manage component dependencies and promote testability. The configuration system now supports environment variable overrides for OPC UA settings.

```
classDiagram
class Dependencies {
+initialize_recoater_client()
+initialize_opcua_service()
+initialize_multilayer_job_manager()
+initialize_websocket_services()
+get_recoater_client()
+get_opcua_service()
+get_multilayer_job_manager()
+get_websocket_handler()
+get_status_poller()
}
class RecoaterClient {
+get_state()
+get_config()
+set_config()
+get_drum()
+set_drum_motion()
+cancel_drum_motion()
+get_drum_motion()
+get_drum_ejection()
+set_drum_ejection()
+get_drum_suction()
+set_drum_suction()
+get_blade_screws_info()
+get_blade_screws_motion()
+set_blade_screws_motion()
+cancel_blade_screws_motion()
+get_leveler_pressure()
+set_leveler_pressure()
+get_leveler_sensor()
+get_layer_parameters()
+set_layer_parameters()
+get_print_job_status()
+start_print_job()
+cancel_print_job()
}
class MockRecoaterClient {
+Implements same interface
+Returns mock data
+Used in development mode
}
class OPCUACoordinator {
+connect()
+disconnect()
+is_connected()
+get_server_status()
+set_job_active()
+set_job_inactive()
+update_layer_progress()
+set_drums_ready()
+set_backend_error()
+clear_error_flags()
+write_variable()
+read_variable()
+subscribe_to_changes()
}
class MultiMaterialJobService {
+start_layer_by_layer_job()
+clear_all_error_flags()
+cancel_job()
+get_job_status()
+get_drum_status()
+setup_opcua_job()
+cleanup_opcua_job()
+reset_opcua_layer_flags()
+update_opcua_layer_progress()
+signal_opcua_layer_complete()
+signal_opcua_ready_to_print()
+handle_job_error()
+wait_for_layer_completion()
+wait_until_error_cleared()
}
class LayerProcessingMixin {
+cancel_job()
+get_job_status()
+get_drum_status()
+clear_job_error_flags()
}
class OPCUACoordinationMixin {
+setup_opcua_job()
+cleanup_opcua_job()
+reset_opcua_layer_flags()
+update_opcua_layer_progress()
+signal_opcua_layer_complete()
+signal_opcua_ready_to_print()
+handle_job_error()
+wait_for_layer_completion()
+clear_error_flags()
+wait_until_error_cleared()
}
class CliCachingMixin {
+has_cached_files()
+get_max_layers()
+cache_cli_file()
+get_layer_data_for_drum()
+get_empty_layer_template()
}
Dependencies --> RecoaterClient : "creates"
Dependencies --> MockRecoaterClient : "creates"
Dependencies --> OPCUACoordinator : "initializes"
Dependencies --> MultiMaterialJobService : "initializes"
MultiMaterialJobService --> LayerProcessingMixin : "inherits"
MultiMaterialJobService --> OPCUACoordinationMixin : "inherits"
MultiMaterialJobService --> CliCachingMixin : "inherits"
MultiMaterialJobService --> RecoaterClient : "uses"
OPCUACoordinator --> OPCUAServerManager : "uses"
```

**Diagram sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [services/recoater_client.py](file://backend/services/recoater_client.py)
- [services/mock_recoater_client.py](file://backend/services/mock_recoater_client.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/services/job_management/mixins/layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py)
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py)
- [backend/app/services/job_management/mixins/cli_caching_mixin.py](file://backend/app/services/job_management/mixins/cli_caching_mixin.py)

**Section sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)

### MultiMaterialJobService Architecture
The MultiMaterialJobService represents the consolidated job management architecture, combining multiple mixins to provide a cohesive interface for multi-material printing jobs. This refactoring improves code organization and separation of concerns.

```
classDiagram
class MultiMaterialJobService {
+recoater_client : RecoaterClient
+opcua : OPCUAService
+job_config : JobConfig
+current_job : MultiMaterialJobState
+cli_cache : Dict[str, CliCacheEntry]
+drum_cli_cache : Dict[int, Dict[str, Any]]
+drum_upload_delay : float
+status_poll_interval : float
+start_layer_by_layer_job() : bool
+_validate_and_setup_job() : int
+_process_all_layers()
+_process_layer(layer_index: int)
+_upload_layer_to_drums(layer_index: int)
+_get_layer_data_for_drum(drum_id: int, layer_index: int) : bytes
+_get_empty_layer_template() : bytes
+_prepare_and_start_print_job(layer_index: int)
+clear_all_error_flags() : bool
}
class LayerProcessingMixin {
+cancel_job() : bool
+get_job_status() : Dict[str, Any]
+get_drum_status(drum_id: int) : Dict[str, Any]
+clear_job_error_flags() : bool
}
class OPCUACoordinationMixin {
+setup_opcua_job(total_layers: int)
+cleanup_opcua_job()
+reset_opcua_layer_flags()
+update_opcua_layer_progress(progress: int)
+signal_opcua_layer_complete()
+signal_opcua_ready_to_print()
+handle_job_error(error: Exception)
+wait_for_layer_completion() : bool
+clear_error_flags() : bool
+wait_until_error_cleared(poll_interval: float) : bool
}
class CliCachingMixin {
+has_cached_files() : bool
+get_max_layers() : int
+cache_cli_file(file_id: str, file_data: bytes) : bool
+get_layer_data_for_drum(drum_id: int, layer_index: int) : bytes
+get_empty_layer_template() : bytes
}
MultiMaterialJobService --> LayerProcessingMixin : "inherits"
MultiMaterialJobService --> OPCUACoordinationMixin : "inherits"
MultiMaterialJobService --> CliCachingMixin : "inherits"
```

**Diagram sources**
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/services/job_management/mixins/layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py)
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py)
- [backend/app/services/job_management/mixins/cli_caching_mixin.py](file://backend/app/services/job_management/mixins/cli_caching_mixin.py)

**Section sources**
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py)

### State Management in Frontend Stores
The frontend uses Pinia for state management, providing a centralized store for application state.

```
classDiagram
class PrintJobStore {
-jobStatus : PrintJobStatusResponse
-layerParameters : LayerParametersResponse
-drumStates : Map<int, DrumStatusResponse>
-fileUploads : Map<int, FileUploadResponse>
-currentJobId : string
-isPrinting : boolean
+updateJobStatus(status)
+setLayerParameters(params)
+updateDrumState(drumId, state)
+recordFileUpload(drumId, response)
+setCurrentJobId(id)
+setIsPrinting(flag)
+clearJobData()
}
class StatusStore {
-systemStatus : Dict
-axisData : Dict
-drumData : Dict
-levelerData : Dict
-printData : Dict
-connectionStatus : boolean
-errorState : string
+updateStatus(data)
+subscribeToWebSocket()
+handleWebSocketMessage(message)
+setConnectionStatus(status)
+setErrorState(error)
+clearErrorState()
}
class ApiService {
-httpClient : AxiosInstance
-websocket : WebSocket
-messageHandlers : Map
+get(endpoint)
+post(endpoint, data)
+put(endpoint, data)
+delete(endpoint)
+uploadCliFile(drumId, file)
+connectWebSocket()
+sendMessage(message)
+addMessageHandler(type, handler)
+removeMessageHandler(type, handler)
}
PrintJobStore --> ApiService : "calls"
StatusStore --> ApiService : "calls"
ApiService --> Backend : "HTTP/WebSocket"
class View {
+mounted()
+beforeUnmount()
}
class PrintView {
+onMounted()
+onBeforeUnmount()
+startPrintJob()
+cancelPrintJob()
+uploadCliFile()
}
class StatusView {
+onMounted()
+onBeforeUnmount()
+updateDisplay()
}
class RecoaterView {
+onMounted()
+onBeforeUnmount()
+controlDrumMotion()
+adjustEjectionPressure()
+adjustSuctionPressure()
}
View <|-- PrintView
View <|-- StatusView
View <|-- RecoaterView
View <|-- ConfigurationView
View <|-- AxisView
PrintView --> PrintJobStore : "accesses"
PrintView --> ApiService : "calls"
StatusView --> StatusStore : "accesses"
StatusView --> ApiService : "calls"
RecoaterView --> ApiService : "calls"
```

**Diagram sources**
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)
- [frontend/src/services/api.js](file://frontend/src/services/api.js)

**Section sources**
- [frontend/src/stores/printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [frontend/src/stores/status.js](file://frontend/src/stores/status.js)

## Dependency Analysis
The system's dependency structure follows a clear hierarchy with well-defined interfaces between components. The configuration flow has been updated to support environment variable overrides, particularly for OPC UA settings.

```
graph TD
backend.app.main.app[FastAPI App] --> backend.app.main.lifespan[Lifespan Manager]
backend.app.main.lifespan --> backend.app.dependencies.initialize_recoater_client[Initialize Recoater Client]
backend.app.main.lifespan --> backend.app.dependencies.initialize_opcua_service[Initialize OPC UA Service]
backend.app.main.lifespan --> backend.app.dependencies.initialize_multilayer_job_manager[Initialize Job Manager]
backend.app.main.lifespan --> backend.app.dependencies.initialize_websocket_services[Initialize WebSocket Services]
backend.app.main.lifespan --> backend.app.services.monitoring.status_poller.start[Start Status Poller]
backend.app.main.lifespan --> backend.app.utils.heartbeat.start_heartbeat_task[Start Heartbeat]
backend.app.main.app --> backend.app.api.status.router[Status Router]
backend.app.main.app --> backend.app.api.axis.router[Axis Router]
backend.app.main.app --> backend.app.api.recoater_controls.router[Recoater Router]
backend.app.main.app --> backend.app.api.print.router[Print Router]
backend.app.main.app --> backend.app.api.configuration.router[Configuration Router]
backend.app.api.status.get_status --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.axis.get_axis_status --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.recoater_controls.get_drum_motion --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.print.get_layer_parameters --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.api.print.get_layer_parameters --> backend.app.dependencies.get_multilayer_job_manager[Get Job Manager]
backend.app.api.configuration.get_configuration --> backend.app.dependencies.get_recoater_client[Get Client]
backend.app.services.monitoring.status_poller._polling_loop --> backend.app.services.monitoring.status_poller._poll_and_broadcast[Poll and Broadcast]
backend.app.services.monitoring.status_poller._poll_and_broadcast --> backend.app.services.monitoring.data_gatherer.gather_all_data[Gather All Data]
backend.app.services.monitoring.status_poller._poll_and_broadcast --> backend.app.services.communication.websocket_manager.broadcast[Broadcast]
backend.app.services.job_management.multimaterial_job_service.start_layer_by_layer_job --> backend.app.services.opcua.opcua_service.set_job_active[Set Job Active]
backend.app.services.job_management.multimaterial_job_service.start_layer_by_layer_job --> backend.app.services.opcua.opcua_service.clear_error_flags[Clear Error Flags]
backend.app.services.job_management.multimaterial_job_service.cancel_job --> backend.app.services.opcua.opcua_service.cleanup_opcua_job[Cleanup OPC UA Job]
backend.app.dependencies.get_recoater_client --> backend.app.dependencies._recoater_client[Global Client]
backend.app.dependencies.get_opcua_service --> backend.app.dependencies._opcua_service[Global Coordinator]
backend.app.dependencies.get_multilayer_job_manager --> backend.app.dependencies._multimaterial_job_service[Global Job Manager]
backend.app.config.opcua_config.get_opcua_config --> backend.app.config.opcua_config.OPCUAServerConfig[Create Config]
backend.app.config.opcua_config.OPCUAServerConfig --> backend.app.config.opcua_config.os.getenv[Environment Variables]
backend.app.main.app --> backend.app.websockets.WebSocketHandler[WebSocket Handler]
backend.app.websockets.WebSocketHandler --> backend.app.services.communication.websocket_manager.WebSocketConnectionManager[WebSocket Manager]
backend.app.dependencies.initialize_websocket_services --> backend.app.dependencies._websocket_manager[WebSocket Manager]
backend.app.dependencies.initialize_websocket_services --> backend.app.dependencies._websocket_handler[WebSocket Handler]
backend.app.dependencies.initialize_websocket_services --> backend.app.dependencies._status_poller[Status Poller]
```

**Diagram sources**
- [backend/app/main.py](file://backend/app/main.py)
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)
- [backend/app/services/monitoring/data_gatherer.py](file://backend/app/services/monitoring/data_gatherer.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

**Section sources**
- [backend/app/dependencies.py](file://backend/app/dependencies.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Performance Considerations
The system implements several performance optimizations to ensure responsive operation:

1. **Singleton Pattern**: WebSocket services and status poller use singleton pattern to prevent multiple instances and resolve circular dependencies.
2. **Concurrent Data Gathering**: The `DataGatherer` service uses `asyncio` to collect status data from multiple sources concurrently, minimizing polling latency.
3. **Subscription-Based Broadcasting**: The `WebSocketManager` filters messages based on client subscriptions, reducing network traffic by only sending relevant data.
4. **Connection Pooling**: The `RecoaterClient` likely maintains HTTP connections to reduce connection overhead for frequent API calls.
5. **Efficient Polling**: The `StatusPoller` service only polls when there are active WebSocket connections, conserving resources during idle periods.
6. **Caching**: The system caches parsed CLI files and maintains job state in memory to avoid repeated processing.
7. **Background Tasks**: Long-running operations like job coordination are handled in background tasks to prevent blocking the main event loop.
8. **Heartbeat Monitoring**: The heartbeat system maintains connection health without requiring constant polling.
9. **Auto-Restart Mechanism**: The OPC UA server includes an auto-restart feature with configurable retry limits and delays, improving system resilience.
10. **Modular WebSocket Architecture**: The extraction of WebSocket functionality into a separate module improves code maintainability and allows for independent optimization of real-time communication components.

**Section sources**
- [backend/app/services/monitoring/data_gatherer.py](file://backend/app/services/monitoring/data_gatherer.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)
- [backend/app/services/communication/websocket_manager.py](file://backend/app/services/communication/websocket_manager.py)
- [backend/app/utils/heartbeat.py](file://backend/app/utils/heartbeat.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)
- [backend/app/dependencies.py](file://backend/app/dependencies.py)

## Troubleshooting Guide
The system includes several mechanisms for error detection and recovery:

1. **Comprehensive Error Handling**: Each API endpoint includes try-catch blocks for `RecoaterConnectionError`, `RecoaterAPIError`, and general exceptions, providing appropriate HTTP status codes.
2. **Health Check Endpoints**: The `/api/v1/status/health` endpoint allows monitoring of both backend and recoater hardware health.
3. **Connection Error Broadcasting**: The `StatusPoller` broadcasts connection errors to all connected clients, enabling the frontend to display appropriate error messages.
4. **OPC UA Error Flags**: The system uses OPC UA variables (`backend_error` and `plc_error`) to coordinate error states between backend and PLC.
5. **Heartbeat Monitoring**: The heartbeat system detects and attempts to recover from lost connections to the recoater hardware.
6. **Retry Logic**: The coordination engine includes retry mechanisms for failed operations with configurable retry limits.
7. **Detailed Logging**: Comprehensive logging at INFO and DEBUG levels helps diagnose issues during development and operation.
8. **OPC UA Server Auto-Restart**: The OPC UA server automatically attempts to restart on failure, with configurable maximum retry attempts and delay between attempts.
9. **Empty Layer Template Handling**: The multilayer job manager now properly handles the empty_layer.cli template file for generating single layers.
10. **WebSocket Connection Management**: The WebSocket architecture includes automatic reconnection logic in the frontend and proper cleanup of disconnected clients in the backend.

**Section sources**
- [backend/app/api/status.py](file://backend/app/api/status.py)
- [backend/app/services/monitoring/status_poller.py](file://backend/app/services/monitoring/status_poller.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/utils/heartbeat.py](file://backend/app/utils/heartbeat.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/services/job_management/multimaterial_job_service.py](file://backend/app/services/job_management/multimaterial_job_service.py)
- [backend/app/websockets.py](file://backend/app/websockets.py)

## Conclusion
The APIRecoater_Ethernet system presents a well-architected client-server application for controlling industrial recoater devices. The system effectively separates concerns between frontend presentation and backend services, using established patterns like dependency injection, service layer abstraction, and event-driven architecture. The integration of OPC UA enables robust communication with industrial PLCs for multi-material print coordination. The recent updates have enhanced the system with a refactored job management architecture using consolidated mixins and clearer separation of concerns. The WebSocket services and status poller have been converted to singleton pattern to resolve circular dependencies and ensure consistent service instances. The coordination_engine and multilayer_job_manager have been split into sub-modules for better organization and maintainability. The real-time capabilities provided by WebSockets ensure operators receive immediate feedback on system status. The modular design allows for future expansion and maintenance, while the comprehensive error handling and monitoring systems promote reliability in industrial environments. The use of modern web technologies (Vue.js 3, FastAPI) ensures a responsive user experience and efficient server-side processing. The refactoring of the WebSocket architecture by extracting websockets.py from main.py into a separate module demonstrates good software engineering practices, improving code organization and maintainability.