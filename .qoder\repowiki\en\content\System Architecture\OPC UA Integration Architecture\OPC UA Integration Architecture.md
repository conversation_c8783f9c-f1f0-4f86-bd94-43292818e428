# OPC UA Integration Architecture

<cite>
**Referenced Files in This Document**   
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py) - *Updated in recent commit*
- [opcua_config.py](file://backend/app/config/opcua_config.py) - *Configuration schema for OPC UA integration*
- [coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py) - *Refactored coordination logic*
- [layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py) - *Updated job status handling*
- [models.py](file://backend/app/services/opcua/models.py) - *Domain models for OPC UA operations*
- [opcua_simulator.py](file://tests/opcua_simulator.py) - *Testing utility for OPC UA interactions*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the 7-variable OPC UA architecture as the single source of truth for job status
- Removed references to legacy CoordinationStatus enum and redundant OPC UA exceptions
- Updated class diagrams to reflect the refactored mixin-based architecture
- Added details about the simplified variable set and their roles in job coordination
- Enhanced documentation of the OPCUAService class and its method resolution order
- Updated sequence diagrams to reflect current job status management workflow
- Removed outdated references to time fields and deprecated components

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Data Mapping and Type Conversion](#data-mapping-and-type-conversion)
7. [Connection Lifecycle and Fault Recovery](#connection-lifecycle-and-fault-recovery)
8. [Network Security and Session Management](#network-security-and-session-management)
9. [Testing and Development Utilities](#testing-and-development-utilities)
10. [Conclusion](#conclusion)

## Introduction

The OPC UA integration layer in APIRecoater_Ethernet implements a dual-role architecture where the backend serves as both an OPC UA server and client. This design enables bidirectional communication between the system's internal components and external industrial devices. As an OPC UA server, it exposes its internal state to SCADA systems and PLCs via `opcua_service.py`. Simultaneously, it acts as an OPC UA client through the job management system to connect to and control the recoater device. This document details the architecture, components, data flows, and operational patterns of this integration layer, providing comprehensive insight into its functionality and design principles.

## Project Structure

The project follows a modular structure with clear separation of concerns. The backend contains the core OPC UA integration components within the `app/services/opcua` directory, with configuration centralized in `app/config`. The OPC UA service is implemented using a mixin-based architecture, with coordination logic separated into reusable components. Testing utilities are available in both backend and root-level test directories.

```mermaid
graph TD
subgraph "Backend"
subgraph "Services"
OPCUAService[opcua_service.py]
end
subgraph "Configuration"
OPCUAConfig[opcua_config.py]
end
subgraph "Mixins"
CoordinationMixin[coordination_mixin.py]
ServerMixin[server_mixin.py]
MonitoringMixin[monitoring_mixin.py]
end
end
subgraph "Utilities"
OPCUASimulator[opcua_simulator.py]
end
OPCUAService --> OPCUAConfig
OPCUAService --> CoordinationMixin
OPCUAService --> ServerMixin
OPCUAService --> MonitoringMixin
OPCUASimulator -.-> OPCUAConfig
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py)
- [backend/app/services/opcua/mixins/server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py)
- [backend/app/services/opcua/mixins/monitoring_mixin.py](file://backend/app/services/opcua/mixins/monitoring_mixin.py)
- [tests/opcua_simulator.py](file://tests/opcua_simulator.py)

## Core Components

The OPC UA integration layer consists of several core components that work together to enable industrial communication. The `OPCUAService` class in `opcua_service.py` implements the server functionality, exposing coordination variables to external SCADA systems. The service uses a mixin-based architecture to separate concerns, with `CoordinationMixin` providing high-level job coordination methods. Configuration is managed through `opcua_config.py`, which defines the server settings and the seven coordination variables. The integration now uses these seven variables as the single source of truth for job status, eliminating redundant status tracking mechanisms.

**Section sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py)

## Architecture Overview

The OPC UA integration architecture implements a dual-role pattern where the backend acts as both server and client. As a server, it hosts seven coordination variables that external PLCs can read and write. As a client, it connects to the recoater device to send commands and receive status updates. This bidirectional communication enables tight integration between the backend system and industrial hardware.

```mermaid
graph TB
subgraph "Backend System"
subgraph "OPC UA Service"
Service[OPCUAService]
end
subgraph "Application Logic"
API[REST API]
JobManager[Job Manager]
end
subgraph "Device Interface"
Client[RecoaterClient]
end
end
subgraph "External Systems"
PLC[TwinCAT PLC]
SCADA[SCADA System]
Recoater[Recoater Device]
end
API --> Service
JobManager --> Service
Service --> PLC
Service --> SCADA
JobManager --> Client
Client --> Recoater
style Service fill:#bbf,stroke:#333
style Client fill:#f96,stroke:#333
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L1-L200)
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py#L1-L250)

## Detailed Component Analysis

### OPC UA Service Implementation

The `OPCUAService` class provides the foundation for OPC UA server functionality, exposing seven coordination variables to external systems. It uses a mixin-based architecture to separate server management, coordination logic, and monitoring concerns. The service implements method resolution order (MRO) carefully to ensure that event handling is properly triggered when variables change.

```mermaid
classDiagram
class OPCUAService {
-config : ServerConfig
-_server_running : bool
-_opcua_server : Optional[Server]
-_variable_nodes : Dict[str, Any]
-_variable_cache : Dict[str, Any]
-_connected : bool
-_monitoring_task : Optional[Task]
-monitoring_active : bool
-_event_handlers : Dict[str, List]
+initialize() bool
+shutdown() bool
+connect() bool
+disconnect() bool
+is_connected() bool
+start_server() bool
+stop_server() bool
+write_variable(name, val) bool
+read_variable(name) Any
+get_server_status() Dict
+get_job_active() bool
+get_total_layers() int
+get_current_layer() int
+get_backend_error() bool
+get_plc_error() bool
+get_recoater_ready_to_print() bool
+get_recoater_layer_complete() bool
+_create_server_variables() bool
+_get_server_config() ServerConfig
+_trigger_event_handlers() None
+_cleanup() None
}
OPCUAService --> ServerConfig : "uses"
OPCUAService --> CoordinationMixin : "inherits"
OPCUAService --> ServerMixin : "inherits"
OPCUAService --> MonitoringMixin : "inherits"
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L1-L200)

**Section sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)

### Coordination Mixin Implementation

The `CoordinationMixin` class provides high-level methods for job coordination using the seven-variable architecture. It abstracts away the complexity of direct variable management and provides convenience methods for common operations. The mixin also implements an event subscription system that allows other components to react to variable changes.

```mermaid
classDiagram
class CoordinationMixin {
-_connected : bool
-_monitoring_task : Optional[Task]
-_event_handlers : Dict[str, List]
+connect() bool
+disconnect() bool
+is_connected() bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+write_variable(name, val) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+_trigger_event_handlers(name, value) None
}
CoordinationMixin --> ServerMixin : "depends on"
CoordinationMixin --> ServerConfig : "uses"
```

**Diagram sources**
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py#L1-L183)

**Section sources**
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py)

### 7-Variable Job Coordination Architecture

The system now uses seven OPC UA variables as the single source of truth for job status, replacing legacy time fields and redundant status tracking. These variables enable bidirectional communication between the backend and PLC.

```mermaid
graph TD
subgraph "Backend-Managed Variables"
job_active["job_active: Boolean"]
total_layers["total_layers: Int32"]
current_layer["current_layer: Int32"]
recoater_ready_to_print["recoater_ready_to_print: Boolean"]
recoater_layer_complete["recoater_layer_complete: Boolean"]
backend_error["backend_error: Boolean"]
end
subgraph "PLC-Managed Variables"
plc_error["plc_error: Boolean"]
end
Backend((Backend)) --> |Writes| job_active
Backend --> |Writes| total_layers
Backend --> |Writes| current_layer
Backend --> |Writes| recoater_ready_to_print
Backend --> |Writes| recoater_layer_complete
Backend --> |Writes| backend_error
Backend <--|Writes| plc_error
job_active --> |Job lifecycle| PLC((PLC))
total_layers --> |Total layer count| PLC
current_layer --> |Current progress| PLC
recoater_ready_to_print --> |Ready signal| PLC
recoater_layer_complete --> |Completion signal| PLC
backend_error --> |Error reporting| PLC
plc_error --> |Error reporting| Backend
style Backend fill:#f9f,stroke:#333
style PLC fill:#f9f,stroke:#333
```

**Diagram sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py#L150-L220)
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py#L50-L100)

**Section sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py)

## Data Mapping and Type Conversion

The integration layer implements a robust data mapping system that translates between OPC UA node values and internal Python models. This mapping is defined in the configuration and ensures type safety across the system boundary.

### Configuration Schema

The `opcua_config.py` file defines the configuration schema for the OPC UA integration, including server settings and the seven coordination variables.

```python
@dataclass
class OPCUAServerConfig:
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

### Variable Definition

The seven coordination variables are defined with their OPC UA metadata, enabling automatic creation and type mapping.

```python
COORDINATION_VARIABLES: List[CoordinationVariable] = [
    CoordinationVariable(
        name="job_active",
        node_id="ns=2;s=job_active",
        data_type="Boolean",
        initial_value=False,
        description="Backend sets TRUE at start, FALSE at end"
    ),
    CoordinationVariable(
        name="total_layers",
        node_id="ns=2;s=total_layers",
        data_type="Int32",
        initial_value=0,
        description="Backend sets once at job start"
    ),
    CoordinationVariable(
        name="current_layer",
        node_id="ns=2;s=current_layer",
        data_type="Int32",
        initial_value=0,
        description="Backend manages, PLC reads"
    ),
    CoordinationVariable(
        name="recoater_ready_to_print",
        node_id="ns=2;s=recoater_ready_to_print",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when Aerosint is ready"
    ),
    CoordinationVariable(
        name="recoater_layer_complete",
        node_id="ns=2;s=recoater_layer_complete",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes when deposition complete"
    ),
    CoordinationVariable(
        name="backend_error",
        node_id="ns=2;s=backend_error",
        data_type="Boolean",
        initial_value=False,
        description="Backend writes if any issue arises"
    ),
    CoordinationVariable(
        name="plc_error",
        node_id="ns=2;s=plc_error",
        data_type="Boolean",
        initial_value=False,
        description="PLC writes if any issues"
    )
]
```

### Type Conversion

The server implements type conversion to ensure data integrity when writing to OPC UA nodes.

```mermaid
flowchart TD
Start([Write Variable]) --> ValidateInput["Validate Input Parameters"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Error Response"]
InputValid --> |Yes| DetermineType["Determine Expected Type"]
DetermineType --> CoerceValue["Coerce Value to Expected Type"]
CoerceValue --> CreateVariant["Create UA Variant"]
CreateVariant --> WriteNode["Write to OPC UA Node"]
WriteNode --> ReturnSuccess["Return Success"]
ReturnError --> End([Function Exit])
ReturnSuccess --> End
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L300-L400)

**Section sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py)
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py)

## Connection Lifecycle and Fault Recovery

The OPC UA integration implements comprehensive connection lifecycle management and fault recovery mechanisms to ensure system reliability.

### Server Lifecycle

The OPC UA server follows a well-defined lifecycle with proper initialization, operation, and cleanup phases.

```mermaid
stateDiagram-v2
[*] --> Initialized
Initialized --> Starting : initialize()
Starting --> Running : Server started
Running --> Stopping : shutdown()
Stopping --> Stopped : Cleanup complete
Running --> Error : Exception
Error --> Restarting : auto_restart enabled
Restarting --> Starting : restart_delay elapsed
Error --> Stopped : auto_restart disabled
Stopped --> [*]
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L145-L150)

### Connection Sequence

The coordinator manages the connection sequence between the backend and OPC UA server.

```mermaid
sequenceDiagram
participant App as "Application"
participant Service as "OPCUAService"
participant Server as "OPC UA Server"
App->>Service : initialize()
Service->>Server : start_server()
Server-->>Service : Success
Service->>Service : connect()
Service->>Service : _connected = true
Service->>Service : Start monitoring
Service-->>App : Success
```

**Diagram sources**
- [backend/app/services/opcua/opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L145-L150)

### Job Status Coordination Sequence

The seven-variable architecture coordinates job status between backend and PLC.

```mermaid
sequenceDiagram
participant Backend as "Backend"
participant PLC as "PLC"
Backend->>Backend : setup_opcua_job(total_layers)
Backend->>PLC : job_active=True, total_layers=N, current_layer=1
loop Layer Processing
Backend->>PLC : recoater_ready_to_print=True
PLC->>Backend : Wait for ready signal
PLC->>PLC : Process layer
PLC->>Backend : plc_error=True (if error)
Backend->>PLC : recoater_layer_complete=True
Backend->>Backend : update_layer_progress(current_layer++)
end
Backend->>PLC : job_active=False
Backend->>Backend : cleanup_opcua_job()
```

**Diagram sources**
- [backend/app/services/job_management/mixins/coordination_mixin.py](file://backend/app/services/job_management/mixins/coordination_mixin.py#L50-L150)
- [backend/app/services/opcua/mixins/coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py#L50-L100)

## Network Security and Session Management

The OPC UA integration provides configurable network security and session management options to meet industrial automation requirements.

### Security Configuration

Security settings are configurable through environment variables or defaults, allowing deployment flexibility.

```python
# Security settings from opcua_config.py
security_policy: str = "None"  # No security for internal network
security_mode: str = "None"
certificate_path: str = ""
private_key_path: str = ""
```

These settings can be overridden via environment variables:
- `OPCUA_SECURITY_POLICY`: Security policy (None, Basic256Sha256, etc.)
- `OPCUA_SECURITY_MODE`: Security mode (None, Sign, SignAndEncrypt)
- `OPCUA_CERTIFICATE_PATH`: Path to server certificate file
- `OPCUA_PRIVATE_KEY_PATH`: Path to private key file

### Session Management

The system implements proper session management with configurable timeouts.

```python
# Connection settings from opcua_config.py
connection_timeout: float = 5.0  # Client connection timeout (seconds)
session_timeout: float = 60.0    # OPC UA session timeout (seconds)
```

The server handles session cleanup through the `_cleanup()` method, which clears variable references and resets the server state.

**Section sources**
- [backend/app/config/opcua_config.py](file://backend/app/config/opcua_config.py#L105-L135)

## Testing and Development Utilities

The system includes several utilities to support testing and development without requiring physical hardware.

### OPC UA Simulator

The `opcua_simulator.py` utility provides a standalone OPC UA server for testing client interactions.

```python
class OPCUAMachineSimulator:
    """
    Simulate an OPC UA server representing an industrial machine.
    
    Supports simulation of different machine types:
    - 3d_printer: Temperature, pressure, humidity, build progress
    - cnc_machine: Spindle speed, vibration, tool wear
    - power_meter: Voltage, current, power readings
    """
```

This simulator allows developers to test OPC UA client code without requiring access to real industrial equipment.

**Section sources**
- [tests/opcua_simulator.py](file://tests/opcua_simulator.py)

### Mock OPC UA Client

The `MockOPCUAClient` provides a simulated OPC UA client for testing server functionality.

```python
class MockOPCUAClient:
    """
    Mock OPC UA client for testing server functionality.
    
    This client can connect to the OPC UA server and simulate
    PLC behavior for testing coordination logic.
    """
```

This client is used in integration tests to validate the server's behavior without requiring a real PLC connection.

**Section sources**
- [backend/tests/mock_opcua_client.py](file://backend/tests/mock_opcua_client.py)

## Conclusion

The OPC UA integration layer in APIRecoater_Ethernet implements a sophisticated dual-role architecture that enables seamless communication between the backend system and industrial automation equipment. By acting as both an OPC UA server and client, the system facilitates bidirectional data exchange with external SCADA systems and the recoater device. The architecture has been simplified to use seven OPC UA variables as the single source of truth for job status, eliminating redundant status tracking mechanisms and improving reliability. The mixin-based design of the `OPCUAService` class promotes code reuse and separation of concerns, while careful attention to method resolution order ensures proper event handling. Data mapping between OPC UA nodes and internal Python models is handled through a well-defined configuration schema that ensures type safety and consistency. The inclusion of simulation and mock utilities enables development and testing without requiring physical hardware, accelerating the development cycle. This integration layer successfully bridges the gap between modern web-based applications and industrial automation standards, providing a robust foundation for industrial IoT applications.