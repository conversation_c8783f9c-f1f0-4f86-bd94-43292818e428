# Job Progress Display

<cite>
**Referenced Files in This Document**   
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue) - *Updated to use backend progress percentage*
- [printJobStore.js](file://frontend/src/stores/printJobStore.js) - *Removed local time calculations*
- [status.js](file://frontend/src/stores/status.js) - *WebSocket integration for real-time updates*
- [multilayer_job.py](file://backend/app/models/multilayer_job.py) - *Source of progress_percentage field*
- [layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py) - *Backend status payload generation*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect removal of local progress calculation and time fields
- Added WebSocket integration details for real-time progress updates
- Removed references to startTime and estimatedTimeRemaining fields
- Updated code examples to show progressPercentage usage
- Enhanced explanation of backend-driven progress calculation
- Added simulation mode detection via WebSocket

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
The **JobProgressDisplay** component in the APIRecoater_Ethernet system provides a real-time visualization of active multi-material print jobs. It displays key metrics such as percentage completion, current layer, and total layers through a progress bar and textual indicators. This component is central to the user experience during print operations, offering immediate feedback on job status and system health. It integrates with the backend via a Pinia store (`printJobStore.js`) that subscribes to real-time updates from the server through WebSocket streaming and periodic polling. The component reflects the state of a complex 3-drum coordination system, where each drum may have different readiness, upload, and processing statuses.

## Project Structure
The project follows a standard Vue 3 + Pinia + Vite frontend architecture with a FastAPI backend. The **JobProgressDisplay** component resides in the `frontend/src/components` directory and is consumed by views such as `PrintView.vue`. It relies on the `printJobStore.js` Pinia store for state management, which in turn receives updates from both HTTP API calls and WebSocket connections. The backend's job management logic is implemented in `multilayer_job_manager.py`, which uses data models defined in `multilayer_job.py`. The API endpoints for job status are exposed in the backend, with real-time updates pushed via WebSocket.

```mermaid
graph TD
subgraph "Frontend"
A[JobProgressDisplay.vue]
B[printJobStore.js]
C[status.js]
D[api.js]
end
subgraph "Backend"
E[WebSocket]
F[multilayer_job_manager.py]
G[multilayer_job.py]
end
A --> B
B --> C
C --> E
E --> B
B --> D
D --> F
F --> G
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)

## Core Components
The **JobProgressDisplay** component is a Vue 3 single-file component that visualizes the progress of a multi-material print job. It imports the `usePrintJobStore` Pinia store to access real-time job status, including `currentLayer`, `totalLayers`, and `progressPercentage`. The component renders a progress bar using the `jobProgress` computed property from the store and displays textual metrics such as layer status. It also handles error states by showing alerts when `backendError` or `plcError` flags are set in the store. The component now relies entirely on backend-provided progress percentage rather than calculating it locally.

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

## Architecture Overview
The system architecture is a client-server model where the frontend Vue application communicates with a FastAPI backend over HTTP and WebSocket. The **JobProgressDisplay** component is part of the presentation layer, which binds to the state managed by the `printJobStore`. This store acts as a facade to the backend, receiving updates through both periodic polling and real-time WebSocket streaming. The backend's `multilayer_job_manager.py` service maintains the current job state and coordinates layer uploads across three drums. Status updates are pushed to the frontend through WebSocket connections, with the `progress_percentage` calculated on the server side.

```mermaid
graph TD
A[JobProgressDisplay.vue] --> B[printJobStore.js]
B --> C[status.js]
C --> D[WebSocket]
D --> E[Backend Job Manager]
E --> F[MultiMaterialJobState]
F --> G[DrumState]
B --> H[HTTP API]
H --> E
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

## Detailed Component Analysis

### JobProgressDisplay.vue Analysis
The **JobProgressDisplay** component is a stateless UI element that renders job progress data from the `printJobStore`. It uses the `jobProgress` computed property to determine the width of the progress bar and displays the percentage in a badge. The component also shows the current layer and total layers in a textual format (e.g., "Layer 15/42"). The component now exclusively uses the `progressPercentage` field provided by the backend, eliminating local calculation of progress. It includes a simulation mode detection that checks WebSocket `print_data` when no active job is present. The component conditionally renders error messages when the store's `hasCriticalError` computed property is true.

```vue
<template>
  <div class="job-progress-display">
    <div class="progress-card">
      <div class="card-header">
        <h3 class="card-title heading-lg">Job Progress</h3>
        <div class="job-info">
          <span v-if="printJobStore.multiMaterialJob.jobId" class="job-id">
            Job ID: {{ printJobStore.multiMaterialJob.jobId }}
          </span>
        </div>
      </div>

      <!-- Simulation banner -->
      <div v-if="!printJobStore.hasActiveJob && isSimulatedPrinting" class="simulation-banner" role="status" aria-live="polite">
        Simulation: printing active (WebSocket)
      </div>

      <div class="card-content">
        <!-- Inline Error Panel inside Job Progress card -->
        <div class="error-panel-spacer">
          <ErrorDisplayPanel />
        </div>
        <!-- Overall Progress -->
        <div class="overall-progress-section">
          <div class="progress-header">
            <h4 class="section-title">Overall Progress</h4>
            <div class="progress-stats">
              <span class="current-layer">
                Layer {{ printJobStore.multiMaterialJob.currentLayer }} of {{ printJobStore.multiMaterialJob.totalLayers }}
              </span>
              <span class="progress-percentage">
                {{ printJobStore.jobProgress }}%
              </span>
            </div>
          </div>

          <div class="progress-bar-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${printJobStore.jobProgress}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)

### printJobStore.js Analysis
The `printJobStore.js` file defines a Pinia store that manages the state of multi-material print jobs. The store's state includes `multiMaterialJob`, which mirrors the `MultiMaterialJobState` from the backend, and `errorFlags` for system-level errors. Key computed properties include `jobProgress`, which now directly uses the `progressPercentage` from the backend rather than calculating it locally. The store receives updates through both HTTP API calls and WebSocket messages via the `statusStore`. The `updateJobStatus` action maps snake_case API fields to camelCase store properties, including the `progress_percentage` field.

```mermaid
classDiagram
class printJobStore {
+multiMaterialJob : Object
+errorFlags : Object
+isLoading : boolean
+isStartingJob : boolean
+isCancellingJob : boolean
+isClearingErrors : boolean
+uploadedFiles : Object
+lastUploadedFiles : Object
+isJobActive : Computed<boolean>
+hasActiveJob : Computed<boolean>
+hasErrors : Computed<boolean>
+hasCriticalError : Computed<boolean>
+allDrumsReady : Computed<boolean>
+allFilesUploaded : Computed<boolean>
+hasMinimumFiles : Computed<boolean>
+canStartJob : Computed<boolean>
+jobProgress : Computed<number>
+updateJobStatus(statusData)
+updateDrumStatus(drumId, drumData)
+setErrorFlags(backendError, plcError, message)
+clearErrorFlags()
+closeCriticalModal()
+setFileUploaded(drumId, fileData)
+clearUploadedFiles()
+setLastUploadedFile(drumId, fileName, source)
+clearLastUploadedFiles()
+getLastUploadedFileName(drumId)
+resetJobState()
+startMultiMaterialJob()
+cancelMultiMaterialJob()
+clearErrorFlagsAPI()
+fetchJobStatus()
+fetchDrumStatus(drumId)
}
```

**Diagram sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)

### Backend Job Management Analysis
The backend's job management is handled by the `MultiMaterialJobManager` class in `multilayer_job_manager.py`. This service creates and manages `MultiMaterialJobState` objects, which contain the current layer, total layers, and progress percentage. The `get_progress_percentage` method calculates the completion percentage based on completed layers (current_layer - 1) divided by total_layers. This calculated value is included in the status payload as `progress_percentage` and sent to the frontend via both HTTP endpoints and WebSocket messages. The manager updates this value after each layer is processed.

```mermaid
classDiagram
class MultiMaterialJobState {
+job_id : str
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+is_active : bool
+status : JobStatus
+error_message : str
+retry_count : int
+drums : Dict[int, DrumState]
+get_progress_percentage() : float
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset()
}
class JobStatus {
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
MultiMaterialJobState --> DrumState : "contains"
```

**Diagram sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py)

**Section sources**
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)
- [layer_operations_mixin.py](file://backend/app/services/job_management/mixins/layer_operations_mixin.py)

### API Integration Analysis
The frontend communicates with the backend through both HTTP API calls and WebSocket streaming. The `printJobStore` receives job status updates from two sources: periodic polling via `fetchJobStatus()` and real-time updates via WebSocket messages handled by `statusStore`. When a WebSocket message with `print_data` is received, the `updateJobStatus` method is called with the job status data. The status payload includes `progress_percentage` calculated on the backend, eliminating the need for client-side calculation. This change improves accuracy and consistency across clients.

```mermaid
sequenceDiagram
participant A as JobProgressDisplay.vue
participant B as printJobStore.js
participant C as status.js
participant D as WebSocket
participant E as Backend
A->>B : Access jobProgress computed property
B->>C : Subscribe to status updates
C->>D : Connect WebSocket
D->>C : Receive print_data message
C->>B : updateJobStatus(jobStatus)
B->>B : Update progressPercentage
B-->>A : State updated (reactive)
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)

## Dependency Analysis
The **JobProgressDisplay** component has a direct dependency on the `printJobStore.js` Pinia store, which depends on `status.js` for WebSocket integration and `api.js` for HTTP communication. The `status.js` store manages the WebSocket connection and propagates job status updates to `printJobStore`. On the backend, the job status data is generated by `multilayer_job_manager.py` and includes the `progress_percentage` field calculated by `MultiMaterialJobState.get_progress_percentage()`. There are no circular dependencies in this chain.

```mermaid
graph TD
A[JobProgressDisplay.vue] --> B[printJobStore.js]
B --> C[status.js]
B --> D[api.js]
C --> E[WebSocket]
D --> F[Backend API]
E --> B
F --> G[multilayer_job_manager.py]
G --> H[multilayer_job.py]
```

**Diagram sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)
- [multilayer_job.py](file://backend/app/models/multilayer_job.py)

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)

## Performance Considerations
The **JobProgressDisplay** component uses computed properties (`jobProgress`, `isJobActive`) which are cached and only re-evaluated when their dependencies change, ensuring efficient rendering. The component now receives `progressPercentage` directly from the backend, eliminating redundant client-side calculations. The store receives updates through WebSocket streaming, providing real-time progress updates without polling overhead. For display smoothness, the progress bar uses CSS transitions for smooth width changes. The component includes a simulation mode detection that uses WebSocket data even when no active job is registered in the store, improving responsiveness during job initialization.

## Troubleshooting Guide
Common issues with the **JobProgressDisplay** component include:
- **Progress not updating**: Verify that the WebSocket connection is active and that `print_data` messages are being received by `statusStore`.
- **Incorrect percentage**: Check that the backend is correctly calculating `progress_percentage` based on (current_layer - 1) / total_layers.
- **Stale display**: Ensure the component is properly subscribed to the Pinia store and that reactivity is not broken by direct state mutation.
- **WebSocket connection issues**: Check that the WebSocket server is running on port 8000 and that the frontend can connect to ws://localhost:8000/ws.

**Section sources**
- [JobProgressDisplay.vue](file://frontend/src/components/JobProgressDisplay.vue)
- [printJobStore.js](file://frontend/src/stores/printJobStore.js)
- [status.js](file://frontend/src/stores/status.js)

## Conclusion
The **JobProgressDisplay** component effectively visualizes the progress of multi-material print jobs by integrating with a well-structured backend system through a reactive Pinia store. Its design follows modern Vue 3 practices, using computed properties for efficient rendering and a clear separation of concerns between presentation, state management, and API communication. The recent changes to use backend-calculated progress percentage improve accuracy and reduce client-side processing. The integration of WebSocket streaming provides real-time updates, enhancing the user experience during print operations. The component provides users with essential feedback on job status, enabling effective monitoring of the printing process.