# OPC UA Client Coordinator

<cite>
**Referenced Files in This Document**   
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py) - *Updated in recent commit*
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py) - *Updated in recent commit*
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py) - *Updated in recent commit*
- [client.py](file://backend\infrastructure\recoater_client\client.py) - *Refactored into modular package*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Updated in recent commit*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the refactoring of coordination functionality into OPCUACoordinationMixin
- Added details about new methods: wait_for_layer_completion, clear_error_flags, and wait_until_error_cleared
- Updated command dispatch workflow to reflect the new async, OPC UA-driven job status workflow
- Added documentation for the 7-variable OPC UA architecture used in job coordination
- Enhanced error handling section with new pause/resume behavior on errors
- Updated class diagrams to reflect the new mixin-based architecture
- Removed references to legacy time fields that were deprecated

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Command Dispatch Workflow](#command-dispatch-workflow)
7. [Connection Parameters and Configuration](#connection-parameters-and-configuration)
8. [Error Handling Strategies](#error-handling-strategies)
9. [OPC UA Variable Operations](#opc-ua-variable-operations)
10. [Conclusion](#conclusion)

## Introduction
The OPC UA Client Coordinator system provides a robust framework for managing communication between a backend application and physical recoater hardware in an additive manufacturing environment. This document details the architecture and functionality of the coordination layer, focusing on how the system manages OPC UA connections, dispatches commands, handles errors, and enables development through mocking capabilities. The system is designed to coordinate multi-material print jobs across multiple drums while maintaining reliable communication with both hardware devices and PLC systems.

## Project Structure
The project follows a layered architecture with clear separation between services, configuration, and business logic. The backend contains the core coordination logic, while the frontend provides a user interface for monitoring and control. The services layer implements the OPC UA coordination, hardware communication, and job management functionality.

```mermaid
graph TD
subgraph "Backend"
subgraph "Services"
OPCUACoordinator[OPCUACoordinationMixin]
OPCUAServer[OPCUAService]
CoordinationEngine[MultiMaterialJobService]
end
subgraph "Configuration"
OPCUAConfig[OPCUAConfig]
end
subgraph "API"
FastAPI[FastAPI Endpoints]
end
end
subgraph "Frontend"
UI[User Interface]
Websocket[WebSocket Manager]
end
FastAPI --> CoordinationEngine
CoordinationEngine --> OPCUACoordinator
OPCUACoordinator --> OPCUAServer
Websocket --> FastAPI
style OPCUACoordinator fill:#f9f,stroke:#333
style OPCUAServer fill:#f9f,stroke:#333
```

**Diagram sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)

## Core Components
The system consists of several core components that work together to provide coordination functionality. The OPCUACoordinationMixin serves as the primary interface for OPC UA communication, while the OPCUAService handles the low-level server operations. The RecoaterClient provides an abstraction layer for hardware communication, and the MultiMaterialJobService manages the overall job coordination process. Configuration is centralized in the OPCUAConfig module, which defines connection parameters and coordination variables.

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)
- [opcua_config.py](file://backend\app\config\opcua_config.py)

## Architecture Overview
The system architecture follows a layered approach with clear separation of concerns. The backend application hosts an OPC UA server that exposes coordination variables to the PLC, while acting as an OPC UA client to communicate with the physical recoater device. This dual role enables bidirectional communication between the backend, PLC, and hardware.

```mermaid
graph TB
subgraph "Backend Application"
subgraph "API Layer"
REST[REST API]
end
subgraph "Coordination Layer"
Coordinator[OPCUACoordinationMixin]
Server[OPCUAService]
end
subgraph "Hardware Interface"
Client[RecoaterClient]
end
end
subgraph "External Systems"
PLC[TwinCAT PLC]
Hardware[Physical Recoater]
end
REST --> Coordinator
Coordinator --> Server
Coordinator --> Client
Server --> PLC
Client --> Hardware
style Coordinator fill:#e6f3ff,stroke:#333
style Server fill:#e6f3ff,stroke:#333
style Client fill:#e6f3ff,stroke:#333
```

**Diagram sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [client.py](file://backend\infrastructure\recoater_client\client.py)

## Detailed Component Analysis

### OPC UA Coordinator Analysis
The OPCUACoordinationMixin class provides a high-level interface for OPC UA communication and coordination with the PLC. It abstracts the complexity of direct OPC UA operations and provides convenience methods for common coordination tasks. This mixin implements the 7-variable OPC UA architecture for job coordination.

#### Class Diagram
```mermaid
classDiagram
class OPCUACoordinationMixin {
+recoater_client : RecoaterClient
+opcua : OPCUAService
+job_config : JobConfig
+status_poll_interval : float
+_resolve_opcua() OPCUAService
+wait_for_layer_completion() bool
+clear_error_flags() bool
+wait_until_error_cleared(poll_interval) bool
+setup_opcua_job(total_layers) None
+cleanup_opcua_job() None
+reset_opcua_layer_flags() None
+update_opcua_layer_progress(progress) None
+signal_opcua_layer_complete() None
+signal_opcua_ready_to_print() None
+handle_job_error(error) None
+_set_error_state(error_message) None
}
class OPCUAService {
+config : ServerConfig
+_server_running : bool
+_connected : bool
+_variable_cache : Dict
+initialize() bool
+shutdown() bool
+connect() bool
+disconnect() bool
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+get_backend_error() bool
+get_plc_error() bool
+get_server_status() Dict
}
OPCUACoordinationMixin --> OPCUAService : "uses"
```

**Diagram sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L200)

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)

### Recoater Client Analysis
The RecoaterClient class provides an abstraction layer for communicating with the physical recoater device. It implements methods for all hardware operations and includes robust error handling and retry logic. The client has been refactored into a modular package under infrastructure/recoater_client, using a mixin-based architecture to organize functionality.

#### Class Diagram
```mermaid
classDiagram
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
+__init__(base_url, timeout)
+_make_request(method, endpoint, return_raw, **kwargs) Any
+get_state() Dict
+set_state(action) Dict
+get_config() Dict
+set_config(config) Dict
+get_drums() Dict
+get_drum(drum_id) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class RecoaterConnectionError {
+__init__(message)
}
class RecoaterAPIError {
+__init__(message)
}
RecoaterClient --> RecoaterConnectionError : "throws"
RecoaterClient --> RecoaterAPIError : "throws"
```

**Diagram sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L331)

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L331)
- [blade_controls.py](file://backend\infrastructure\recoater_client\blade_controls.py#L1-L127)
- [print_controls.py](file://backend\infrastructure\recoater_client\print_controls.py#L1-L88)
- [file_management.py](file://backend\infrastructure\recoater_client\file_management.py#L1-L54)
- [async_client.py](file://backend\infrastructure\recoater_client\async_client.py#L1-L164)

### Mock Recoater Client Analysis
The MockRecoaterClient enables development and testing without requiring access to physical hardware. It simulates the behavior of the real recoater client and provides predictable responses for testing purposes.

```mermaid
classDiagram
class MockRecoaterClient {
+base_url : str
+__init__(base_url)
+get_state() Dict
+set_state(action) Dict
+get_config() Dict
+set_config(config) Dict
+get_drums() Dict
+get_drum(drum_id) Dict
+health_check() bool
+get_drum_motion(drum_id) Dict
+set_drum_motion(drum_id, mode, speed, distance, turns) Dict
+cancel_drum_motion(drum_id) Dict
+get_drum_ejection(drum_id, unit) Dict
+set_drum_ejection(drum_id, target, unit) Dict
+get_drum_suction(drum_id) Dict
+set_drum_suction(drum_id, target) Dict
+get_blade_screws_info(drum_id) Dict
+get_blade_screws_motion(drum_id) Dict
+set_blade_screws_motion(drum_id, mode, distance) Dict
+cancel_blade_screws_motion(drum_id) Dict
+get_blade_screw_info(drum_id, screw_id) Dict
+get_blade_screw_motion(drum_id, screw_id) Dict
+set_blade_screw_motion(drum_id, screw_id, distance) Dict
+cancel_blade_screw_motion(drum_id, screw_id) Dict
+get_leveler_pressure() Dict
+set_leveler_pressure(target) Dict
+get_leveler_sensor() Dict
+get_layer_parameters() Dict
+set_layer_parameters(filling_id, speed, powder_saving, x_offset) Dict
+get_layer_preview() bytes
+start_print_job() Dict
+cancel_print_job() Dict
+upload_drum_geometry(drum_id, file_data, content_type) Dict
+download_drum_geometry(drum_id) bytes
+delete_drum_geometry(drum_id) Dict
+get_print_job_status() Dict
+upload_cli_data(drum_id, cli_data) Dict
+get_drum_status(drum_id) Dict
+get_multimaterial_status() Dict
+monitor_drum_state_transitions(drum_id, expected_states, timeout, poll_interval) bool
}
class RecoaterClient {
+base_url : str
+timeout : float
+session : requests.Session
}
MockRecoaterClient --|> RecoaterClient : "inherits"
```

**Section sources**
- [mock_recoater_client.py](file://backend\infrastructure\mock_recoater_client.py)

## Command Dispatch Workflow
The command dispatch workflow follows a clear path from API request to hardware execution. This sequence diagram illustrates the flow of commands through the system, following the 7-variable OPC UA architecture.

```mermaid
sequenceDiagram
participant API as "API Request"
participant Engine as "MultiMaterialJobService"
participant Coordinator as "OPCUACoordinationMixin"
participant Client as "RecoaterClient"
participant Hardware as "Physical Device"
API->>Engine : start_layer_by_layer_job()
Engine->>Coordinator : setup_opcua_job()
Coordinator->>Coordinator : set_job_active(total_layers)
Coordinator->>Coordinator : update_layer_progress(1)
Engine->>Coordinator : _process_all_layers()
loop Each Layer
Coordinator->>Coordinator : reset_opcua_layer_flags()
Engine->>Client : _upload_layer_to_drums()
Client->>Client : _make_request()
Client->>Hardware : HTTP PUT /drums/{id}/geometry
Hardware-->>Client : 200 OK
Client-->>Engine : Success Response
Coordinator->>Coordinator : signal_opcua_ready_to_print()
Client->>Hardware : start_print_job()
Coordinator->>Coordinator : wait_for_layer_completion()
Hardware->>Coordinator : Polling state until ready
Coordinator->>Coordinator : signal_opcua_layer_complete()
Coordinator->>Coordinator : update_layer_progress(next)
end
Engine->>Coordinator : cleanup_opcua_job()
Coordinator->>Coordinator : set_job_inactive()
```

**Diagram sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L1-L333)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L331)

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L1-L333)

## Connection Parameters and Configuration
The system's connection parameters and configuration are centralized in the opcua_config.py file, which defines the settings for the OPC UA server and coordination variables.

### Configuration Structure
```mermaid
erDiagram
OPCUAServerConfig {
string endpoint PK
string server_name
string namespace_uri
int namespace_idx
string security_policy
string security_mode
string certificate_path
string private_key_path
float connection_timeout
float session_timeout
boolean auto_restart
float restart_delay
int max_restart_attempts
}
CoordinationVariable {
string name PK
string node_id
string data_type
any initial_value
boolean writable
string description
}
OPCUAServerConfig ||--o{ CoordinationVariable : "contains"
```

### Configuration Parameters
**OPC UA Server Configuration**
- **endpoint**: "opc.tcp://0.0.0.0:4843/recoater/server/"
- **server_name**: "Recoater Multi-Material Coordination Server"
- **namespace_uri**: "http://recoater.backend.server"
- **namespace_idx**: 2
- **security_policy**: "None"
- **security_mode**: "None"
- **connection_timeout**: 5.0 seconds
- **session_timeout**: 60.0 seconds
- **auto_restart**: true
- **restart_delay**: 5.0 seconds
- **max_restart_attempts**: 3

**Coordination Variables (7-Variable Architecture)**
- **job_active**: Boolean, initial_value=False, writable=True
- **total_layers**: Int32, initial_value=0, writable=True
- **current_layer**: Int32, initial_value=0, writable=True
- **recoater_ready_to_print**: Boolean, initial_value=False, writable=True
- **recoater_layer_complete**: Boolean, initial_value=False, writable=True
- **backend_error**: Boolean, initial_value=False, writable=True
- **plc_error**: Boolean, initial_value=False, writable=True

**Section sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L1-L294)

## Error Handling Strategies
The system implements comprehensive error handling strategies at multiple levels to ensure reliability and fault tolerance.

### Error Handling Flowchart
```mermaid
flowchart TD
Start([Request]) --> ValidateInput["Validate Input Parameters"]
ValidateInput --> InputValid{"Input Valid?"}
InputValid --> |No| ReturnError["Return Error Response"]
InputValid --> |Yes| ExecuteRequest["Execute Request"]
ExecuteRequest --> Success{"Success?"}
Success --> |Yes| ReturnSuccess["Return Success Response"]
Success --> |No| CheckErrorType{"Error Type?"}
CheckErrorType --> |Connection| HandleConnectionError["Handle Connection Error"]
CheckErrorType --> |API| HandleAPIError["Handle API Error"]
CheckErrorType --> |Coordination| HandleCoordinationError["Handle Coordination Error"]
HandleConnectionError --> Retry{"Retry?"}
Retry --> |Yes| Delay["Wait retry_delay"]
Delay --> ExecuteRequest
Retry --> |No| LogError["Log Error"]
LogError --> UpdateStatus["Update Error Status"]
UpdateStatus --> ReturnError
HandleAPIError --> LogError
HandleCoordinationError --> LogError
style Start fill:#f9f,stroke:#333
style ReturnSuccess fill:#cfc,stroke:#333
style ReturnError fill:#fcc,stroke:#333
```

### Error Types and Handling
**RecoaterConnectionError**
- Raised when connection to recoater hardware fails
- Handled with retry logic (2 attempts, 500ms delay) configurable via RECOATER_MAX_ATTEMPTS and RECOATER_RETRY_DELAY environment variables
- Logged with warning level for transient failures
- Logged with error level for final failures

**RecoaterAPIError**
- Raised when recoater API returns an error response
- Includes status code and response text
- No retry logic (indicates client or server error)
- Triggers error state in coordination engine

**CoordinationError**
- Raised when coordination operations fail
- Wraps lower-level exceptions
- Updates coordination engine state to ERROR
- Sets backend_error flag in OPC UA server

**OPC UA Server Errors**
- Handled with auto-restart capability
- Configurable restart attempts (default: 3)
- Configurable restart delay (default: 5.0 seconds)
- Heartbeat monitoring for server health

**Error Handling Workflow**
- When an error occurs, the system sets the backend_error flag via set_backend_error(True)
- The job state is updated to ERROR
- The system enters a paused state and waits for operator intervention
- The wait_until_error_cleared method blocks until both backend_error and plc_error flags are False
- After error clearance, the current layer is retried automatically
- This pause/resume behavior allows operators to address issues without restarting the entire job

**Section sources**
- [client.py](file://backend\infrastructure\recoater_client\client.py#L1-L331)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L200)

## OPC UA Variable Operations
The system provides methods for reading and writing OPC UA variables, which serve as the communication channel between the backend and PLC.

### Variable Operations Sequence
```mermaid
sequenceDiagram
participant App as "Application"
participant Coordinator as "OPCUACoordinationMixin"
participant Server as "OPCUAService"
participant PLC as "TwinCAT PLC"
App->>Coordinator : setup_opcua_job(150)
Coordinator->>Server : set_job_active(150)
Server->>Server : Write job_active=True, total_layers=150, current_layer=1
Server-->>Coordinator : Success
Coordinator-->>App : Success
PLC->>Server : Read job_active variable
Server-->>PLC : Returns True
PLC->>Server : Write plc_error = True
Server->>Server : node.write_value(True)
Server->>Coordinator : Variable changed event
Coordinator->>Coordinator : _trigger_event_handlers()
Coordinator->>App : Event handlers notified
```

### Example Variable Operations
**Writing Variables**
```python
# Set job as active with 150 layers
await coordinator.setup_opcua_job(150)

# Update current layer progress
await coordinator.update_opcua_layer_progress(5)

# Signal that recoater is ready to print
await coordinator.signal_opcua_ready_to_print()

# Set backend error flag
await coordinator.handle_job_error(exception)
```

**Reading Variables**
```python
# Read current layer number
current_layer = coordinator.get_current_layer()

# Check if job is active
job_active = coordinator.get_job_active()

# Check for PLC errors
plc_error = coordinator.get_plc_error()
```

**Subscribing to Variable Changes**
```python
# Define event handler
async def on_error_change(variable_name, value):
    if variable_name == "backend_error" and value:
        print("Backend error detected!")
    elif variable_name == "plc_error" and value:
        print("PLC error detected!")

# Subscribe to error variables
await coordinator.subscribe_to_changes(
    ["backend_error", "plc_error"], 
    on_error_change
)
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L200)

## Conclusion
The OPC UA Client Coordinator system provides a robust and flexible framework for managing communication between a backend application and physical recoater hardware. By implementing a layered architecture with clear separation of concerns, the system enables reliable coordination of multi-material print jobs while maintaining compatibility with PLC systems through OPC UA. The use of configuration-driven parameters, comprehensive error handling, and mocking capabilities for development makes this system well-suited for industrial additive manufacturing applications. The design allows for easy extension and maintenance, ensuring long-term viability in production environments.