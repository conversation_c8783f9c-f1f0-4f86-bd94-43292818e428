# Multilayer Job Manager

<cite>
**Referenced Files in This Document**   
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py) - *Updated in recent commit 4f0a4564050e852cf32b20133ea2b08182c76dfc*
- [job_config.py](file://backend\app\config\job_config.py) - *Updated in recent commit 6a04839291e1170f0ec66d4e3576e04a6bb0ad3e*
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py) - *Refactored in recent commit*
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py) - *Refactored in recent commit*
- [cli_caching_mixin.py](file://backend\app\services\job_management\mixins\cli_caching_mixin.py) - *Refactored in recent commit*
- [empty_layer.cli](file://backend\app\templates\empty_layer.cli) - *Updated in recent commit e3fbefd7c14703038e9736a89c4de082c45006ec*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect architectural refactoring of job management service
- Replaced outdated `MultiMaterialJobManager` with new `MultiMaterialJobService` class
- Updated core components section to reflect mixin-based architecture
- Revised job lifecycle management to align with new `start_layer_by_layer_job` workflow
- Updated configuration section to reflect environment-driven settings from `.env` file
- Added details about `JobConfig` class and centralized configuration management
- Updated error handling section to reflect unified `clear_all_error_flags` method
- Removed outdated references to `coordination_engine.py` and updated integration details
- Added new code examples from refactored service implementation
- Updated performance considerations to reflect new caching architecture

## Table of Contents
1. [Introduction](#introduction)
2. [Core Components](#core-components)
3. [Domain Model: MultilayerJob](#domain-model-multilayerjob)
4. [Job Lifecycle Management](#job-lifecycle-management)
5. [API Integration](#api-integration)
6. [Coordination Engine Integration](#coordination-engine-integration)
7. [Error Handling and Recovery](#error-handling-and-recovery)
8. [Configuration and Validation](#configuration-and-validation)
9. [Performance Considerations](#performance-considerations)
10. [Testing and Validation](#testing-and-validation)

## Introduction
The Multilayer Job Manager service is a core component of the APIRecoater_Ethernet system, responsible for orchestrating multi-material print jobs across a 3-drum recoater system. This service manages the complete lifecycle of multi-layer print jobs, from initialization and layer progression to completion tracking and error recovery. It serves as the central coordination point between the API layer, the OPC UA coordinator, and the underlying hardware systems, ensuring synchronized operation across multiple material drums during the printing process.

The service implements a stateful job management system that tracks progress across all drums, handles material-specific configurations, and provides robust error handling for complex multi-material printing workflows. Recent refactoring has consolidated functionality into the `MultiMaterialJobService` class, which inherits from three mixins: `LayerProcessingMixin`, `OPCUACoordinationMixin`, and `CliCachingMixin`. This unified architecture provides a cohesive interface for multi-material jobs while maintaining separation of concerns. Configuration values have been moved from hardcoded magic numbers to environment variables, with `job_config.py` centralizing all job orchestration settings.

## Core Components

The Multilayer Job Manager consists of several key components that work together to manage multi-layer print jobs:

- **MultiMaterialJobService**: The primary service class that manages job state, lifecycle, and coordination by inheriting from three mixins
- **LayerProcessingMixin**: Provides job lifecycle management, layer upload operations, and hardware communication
- **OPCUACoordinationMixin**: Handles all OPC UA variable management and coordination logic for the 7-variable architecture
- **CliCachingMixin**: Provides unified caching system for CLI files in multi-material printing workflows
- **JobConfig**: Centralized configuration class that loads settings from environment variables
- **MultiMaterialJobState**: The domain model representing the complete state of a multi-material print job

These components work in concert to provide a comprehensive job management system that handles the complexities of multi-material printing with precision and reliability.

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L1-L333)
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py#L1-L273)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [cli_caching_mixin.py](file://backend\app\services\job_management\mixins\cli_caching_mixin.py#L1-L209)

## Domain Model: MultilayerJob

The domain model for the Multilayer Job Manager is centered around the `MultiMaterialJobState` dataclass, which encapsulates all state information for a multi-material print job. This model provides a comprehensive representation of job state, enabling persistent tracking throughout the entire printing process.

```
classDiagram
class MultiMaterialJobState {
+job_id : str
+file_ids : Dict[int, str]
+total_layers : int
+current_layer : int
+remaining_layers : Dict[int, List[LayerData]]
+header_lines : Dict[int, List[str]]
+is_active : bool
+status : JobStatus
+start_time : Optional[float]
+last_layer_time : Optional[float]
+estimated_completion : Optional[float]
+waiting_for_print_start : bool
+waiting_for_layer_complete : bool
+error_message : str
+retry_count : int
+drums : Dict[int, DrumState]
+get_progress_percentage() float
}
class DrumState {
+drum_id : int
+status : str
+ready : bool
+uploaded : bool
+current_layer : int
+total_layers : int
+error_message : str
+file_id : Optional[str]
+last_update_time : float
+reset() void
}
class LayerData {
+layer_number : int
+cli_data : bytes
+is_empty : bool
+upload_time : Optional[float]
+completion_time : Optional[float]
}
class JobStatus {
+IDLE : str
+INITIALIZING : str
+WAITING_FOR_PRINT_START : str
+RECOATER_ACTIVE : str
+WAITING_FOR_DEPOSITION : str
+LAYER_COMPLETE : str
+JOB_COMPLETE : str
+ERROR : str
+CANCELLED : str
+RUNNING : str
}
MultiMaterialJobState "1" *-- "3" DrumState : contains
MultiMaterialJobState "1" *-- "many" LayerData : contains
MultiMaterialJobState --> JobStatus : status
```

**Diagram sources**
- [multilayer_job.py](file://backend\app\models\multilayer_job.py#L1-L110)

**Section sources**
- [multilayer_job.py](file://backend\app\models\multilayer_job.py#L1-L110)

### Job Attributes

The `MultiMaterialJobState` model includes several key attributes that define the job's configuration and progress:

**Basic Identification**
- **job_id**: Unique identifier for the job, generated using UUID
- **file_ids**: Dictionary mapping drum_id (0, 1, 2) to corresponding CLI file IDs

**Job Scope and Progress**
- **total_layers**: Total number of layers in the job (calculated as maximum across all drums)
- **current_layer**: Current layer being processed (1-based indexing)
- **remaining_layers**: Dictionary mapping drum_id to list of LayerData objects for remaining layers
- **header_lines**: Dictionary mapping drum_id to list of CLI header lines

**Job Status**
- **is_active**: Boolean indicating whether the job is currently active
- **status**: Current JobStatus (e.g., IDLE, RUNNING, ERROR, COMPLETED)
- **start_time**: Timestamp when the job started
- **last_layer_time**: Timestamp of the last completed layer
- **estimated_completion**: Estimated time of job completion

**Process State Tracking**
- **waiting_for_print_start**: Flag indicating if waiting for print start signal
- **waiting_for_layer_complete**: Flag indicating if waiting for layer completion
- **error_message**: Error message if job is in ERROR state
- **retry_count**: Number of retry attempts for failed operations

**Drum Coordination**
- **drums**: Dictionary mapping drum_id (0, 1, 2) to DrumState objects, enabling individual drum state tracking

### Layer Data Structure

Each layer in the print job is represented by a `LayerData` object containing:

- **layer_number**: The layer number (1-based)
- **cli_data**: The ASCII CLI data for the layer as bytes
- **is_empty**: Boolean flag indicating if the layer is empty (used for depleted drums)
- **upload_time**: Timestamp when the layer was uploaded (optional)
- **completion_time**: Timestamp when the layer was completed (optional)

This structure enables efficient storage and retrieval of layer-specific information while supporting the tracking of layer processing times for performance monitoring.

## Job Lifecycle Management

The Multilayer Job Manager implements a comprehensive lifecycle management system for multi-layer print jobs, handling all stages from creation to completion.

### Job Creation

Job creation is now handled through the drum-specific CLI caching system, where files are cached for each drum before job initialization. The process involves:

```
flowchart TD
Start([File Upload]) --> CacheDrumFile["Cache CLI File for Specific Drum"]
CacheDrumFile --> ValidateFile["Validate and Parse CLI File"]
ValidateFile --> StoreInCache["Store in drum_cli_cache"]
StoreInCache --> CheckAllDrums["Check if All Required Drums Have Files"]
CheckAllDrums --> FilesReady{"All Files Cached?"}
FilesReady --> |No| WaitMoreFiles["Wait for Additional Files"]
FilesReady --> |Yes| StartJob["Start Layer-by-Layer Job"]
StartJob --> ValidateAndSetup["Validate and Setup Job State"]
ValidateAndSetup --> SetupOPCUA["Setup OPC UA Job Variables"]
SetupOPCUA --> ProcessAllLayers["Process All Layers Sequentially"]
ProcessAllLayers --> Cleanup["Cleanup OPC UA Job State"]
Cleanup --> End([Job Complete])
WaitMoreFiles --> CacheDrumFile
```

**Diagram sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L150-L200)
- [cli_caching_mixin.py](file://backend\app\services\job_management\mixins\cli_caching_mixin.py#L100-L150)

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L150-L200)
- [cli_caching_mixin.py](file://backend\app\services\job_management\mixins\cli_caching_mixin.py#L100-L150)

The job creation process has been refactored to use a caching-first approach:

1. **Drum-Specific Caching**: CLI files are cached individually for each drum using `cache_cli_file_for_drum`
2. **Validation**: Files are validated and parsed during the caching process
3. **Job Initialization**: The `start_layer_by_layer_job` method initializes the job state when all required files are cached
4. **Layer Count Calculation**: The total number of layers is determined by taking the maximum layer count across all cached drums
5. **Empty Layer Handling**: Missing drum data is handled by the `_get_empty_layer_template` method which reads from the configured template path

### Job Execution Flow

Once a job is started, it executes through a coordinated process that advances through each layer:

```
sequenceDiagram
participant API as "API Layer"
participant JobService as "MultiMaterialJobService"
participant OPCUA as "OPC UA Coordinator"
participant Drums as "Recoater Drums"
API->>JobService : start_layer_by_layer_job()
JobService->>JobService : _validate_and_setup_job()
JobService->>JobService : setup_opcua_job(total_layers)
JobService->>JobService : _process_all_layers()
loop For Each Layer
JobService->>JobService : _process_layer(layer_index)
JobService->>JobService : reset_opcua_layer_flags()
JobService->>JobService : _upload_layer_to_drums(layer_index)
loop For Each Drum (0,1,2)
JobService->>JobService : _get_layer_data_for_drum(drum_id, layer_index)
JobService->>Drums : upload_drum_geometry(drum_id, cli_data)
Drums-->>JobService : Upload Result
end
JobService->>JobService : _prepare_and_start_print_job(layer_index)
JobService->>OPCUA : set_recoater_ready_to_print(True)
JobService->>JobService : wait_for_layer_completion()
JobService->>OPCUA : set_recoater_layer_complete(True)
JobService->>OPCUA : update_layer_progress(next_layer)
end
JobService->>JobService : mark_completed()
JobService->>OPCUA : set_job_inactive()
```

**Diagram sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L202-L280)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L150-L200)

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L202-L280)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L150-L200)

The execution flow demonstrates the coordinated nature of multi-material printing, where each layer cycle involves uploading geometry data to all drums, waiting for readiness, signaling the print start, and confirming completion before advancing to the next layer.

### Layer Advancement

Layer advancement is managed by the `_process_all_layers` method, which handles the distribution of layer data to all drums with appropriate coordination:

```python
async def _process_all_layers(self):
    """
    Process all layers sequentially following the exact workflow.

    For each layer:
    - Reset OPC UA layer flags
    - Upload layer data to each drum (with delay between drums)
    - Signal recoater ready to print
    - Start print job
    - Wait for layer completion via polling
    - Signal layer complete
    - Update current layer progress
    """
    max_layers = self.get_max_layers()

    for layer_index in range(max_layers):
        # Respect cancellation before starting a new layer
        if getattr(self, 'current_job', None) and not self.current_job.is_active:
            logger.info("Job cancelled; stopping before processing next layer")
            return

        layer_num = layer_index + 1
        logger.info(f"Processing layer {layer_num}/{max_layers}")

        # Process this layer
        await self._process_layer(layer_index)

        # If cancelled during the layer, exit gracefully
        if getattr(self, 'current_job', None) and not self.current_job.is_active:
            logger.info("Job cancelled; exiting after current layer")
            return

        # Update current layer progress (cap at total_layers to avoid >100%)
        next_progress = min(layer_num + 1, max_layers)
        await self.update_opcua_layer_progress(next_progress)

    logger.info("All layers processed successfully")
```

This method ensures that layer data is processed sequentially with proper cancellation handling and progress tracking.

## API Integration

The Multilayer Job Manager integrates with the API layer through the print.py endpoint, which provides RESTful interfaces for job management operations.

### Job Configuration via API

The API layer now interacts with the refactored service through the drum caching system:

```python
@router.post("/drum/{drum_id}/cache", response_model=CacheResponse)
async def cache_drum_file(
    drum_id: int,
    file_id: str = Body(..., embed=True),
    job_service: MultiMaterialJobService = Depends(get_multimaterial_job_service)
) -> CacheResponse:
    """
    Cache a CLI file for a specific drum in the multi-material workflow.
    
    Args:
        drum_id: Target drum (0, 1, or 2)
        file_id: ID of the file to cache from generic cache
        job_service: Injected job service instance
        
    Returns:
        Response indicating success or failure
    """
    try:
        # Retrieve from generic cache
        parsed_file = job_service.get_cli_file(file_id)
        if not parsed_file:
            raise HTTPException(status_code=404, detail=f"File {file_id} not found in cache")
            
        # Cache for specific drum
        filename = job_service.get_cli_file_with_metadata(file_id)['original_filename']
        job_service.cache_cli_file_for_drum(drum_id, parsed_file, filename)
        
        return CacheResponse(
            success=True,
            message=f"Successfully cached file for drum {drum_id}",
            drum_id=drum_id,
            layer_count=len(parsed_file.layers)
        )
        
    except Exception as e:
        logger.error(f"Failed to cache file for drum {drum_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Cache failed: {e}")
```

This integration allows clients to build multi-material jobs by caching files for specific drums, providing flexibility in material configuration.

### Status Reporting

The job manager provides comprehensive status reporting through the `get_job_status` method:

```python
async def get_job_status(self) -> Optional[Dict[str, Any]]:
    """Get current job status using OPC UA as the source of truth.

    Returns a dict matching MultiMaterialJobStatusResponse fields.
    """
    # Attempt to read live values from OPC UA
    opcua = None
    try:
        opcua = self._resolve_opcua() if hasattr(self, "_resolve_opcua") else None
    except Exception:
        opcua = None

    # Defaults when OPC UA not available
    job_active = False
    total_layers = 0
    current_layer = 0
    backend_error = False
    plc_error = False
    ready_to_print = False
    layer_complete = False

    if opcua:
        try:
            job_active = bool(await opcua.read_variable("job_active"))
            total_layers = int(await opcua.read_variable("total_layers") or 0)
            current_layer = int(await opcua.read_variable("current_layer") or 0)
            backend_error = bool(await opcua.read_variable("backend_error") or False)
            plc_error = bool(await opcua.read_variable("plc_error") or False)
            ready_to_print = bool(await opcua.read_variable("recoater_ready_to_print") or False)
            layer_complete = bool(await opcua.read_variable("recoater_layer_complete") or False)
        except Exception as e:
            logger.warning(f"Failed to read OPC UA variables for status: {e}")

    # Compute progress percentage
    progress_percentage = 0.0
    if total_layers > 0:
        progress_percentage = max(0.0, min(100.0, round((current_layer / total_layers) * 100.0, 2)))

    # Derive status string using recoater hardware state
    recoater_state = None
    try:
        recoater_resp = await asyncio.to_thread(self.recoater_client.get_state)
        if isinstance(recoater_resp, dict):
            recoater_state = recoater_resp.get("state")
    except Exception as e:
        logger.debug(f"Could not retrieve recoater state for status derivation: {e}")

    # Map to high-level status
    if backend_error or plc_error:
        status = "error"
    elif not job_active:
        status = "idle"
    else:
        if recoater_state == "printing":
            status = "printing"
        else:
            status = "ready" if ready_to_print and not layer_complete else "ready"

    # Compose drums from in-memory job
    drums_payload: Dict[int, Dict[str, Any]] = {}
    job_id = None
    if getattr(self, "current_job", None):
        job = self.current_job
        job_id = job.job_id
        for drum_id, drum in job.drums.items():
            drums_payload[drum_id] = {
                "drum_id": drum.drum_id,
                "status": drum.status,
                "ready": drum.ready,
                "uploaded": drum.uploaded,
                "current_layer": drum.current_layer,
                "total_layers": drum.total_layers,
                "error_message": drum.error_message,
                "file_id": drum.file_id,
            }

    error_message = ""
    if backend_error or plc_error:
        flags = []
        if backend_error:
            flags.append("backend_error")
        if plc_error:
            flags.append("plc_error")
        error_message = ", ".join(flags)

    return {
        "job_id": job_id,
        "is_active": job_active,
        "status": status,
        "current_layer": current_layer,
        "total_layers": total_layers,
        "progress_percentage": progress_percentage,
        "error_message": error_message,
        "drums": drums_payload,
    }
```

This method returns a comprehensive overview of the current job, including overall progress, current layer, and status of all drums by integrating data from both OPC UA variables and in-memory job state.

## Coordination Engine Integration

The Multilayer Job Manager integrates with the OPC UA coordinator to enable automated job execution and layer progression. This integration provides a higher level of automation beyond manual layer-by-layer control.

### OPC UA Coordination Interface

The job manager uses the 7-variable OPC UA architecture for coordination:

```python
class OPCUACoordinationMixin:
    """
    7-Variable OPC UA Workflow:
    1. job_active: Backend sets TRUE at start, FALSE at end
    2. total_layers: Backend sets once at job start
    3. current_layer: Backend manages, PLC reads
    4. recoater_ready_to_print: Backend writes when Aerosint is ready
    5. recoater_layer_complete: Backend writes when deposition complete
    6. backend_error: Backend writes if any issue arises
    7. plc_error: PLC writes if any issues
    """
```

The `OPCUACoordinationMixin` provides methods to manage these variables:

```python
async def setup_opcua_job(self, total_layers: int) -> None:
    """Setup OPC UA job_active=True, total_layers=max_layers, current_layer=1."""
    try:
        opcua = self._resolve_opcua()
        if opcua:
            await opcua.set_job_active(total_layers)
            await opcua.update_layer_progress(1)  # Start at layer 1
            logger.info(f"OPC UA job setup complete: {total_layers} layers")
    except Exception as e:
        logger.warning(f"Failed to setup OPC UA job: {e}")
```

### State Synchronization

The job manager maintains synchronization with the OPC UA coordinator by updating variables that reflect the current job state:

```python
async def signal_opcua_ready_to_print(self) -> None:
    """Signal that recoater is ready to print."""
    try:
        opcua = self._resolve_opcua()
        if opcua:
            await opcua.set_recoater_ready_to_print(True)
    except Exception as e:
        logger.warning(f"Failed to signal ready to print: {e}")

async def signal_opcua_layer_complete(self) -> None:
    """Signal completion of the current layer."""
    try:
        opcua = self._resolve_opcua()
        if opcua:
            await opcua.set_recoater_layer_complete(True)
    except Exception as e:
        logger.warning(f"Failed to signal layer complete: {e}")
```

By updating OPC UA variables, the job manager ensures that the PLC and other system components are aware of the current job state, enabling coordinated operation across the entire system.

## Error Handling and Recovery

The Multilayer Job Manager implements comprehensive error handling strategies to ensure robust operation in the face of failures during multi-layer printing.

### Error States and Recovery

The system defines several error states and provides mechanisms for error recovery:

```
stateDiagram-v2
[*] --> IDLE
IDLE --> READY : files_cached
READY --> RUNNING : start_layer_by_layer_job()
RUNNING --> ERROR : upload_failure
RUNNING --> CANCELLED : cancel_job()
ERROR --> READY : clear_all_error_flags()
CANCELLED --> IDLE : job_reset
RUNNING --> COMPLETED : all_layers_processed
ERROR --> RUNNING : resume_after_recovery
```

**Diagram sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L300-L330)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L100-L150)

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L300-L330)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L100-L150)

The error handling system includes:

- **Error Detection**: Comprehensive exception handling around critical operations
- **Error State Tracking**: Persistent storage of error messages in the job state
- **Operator Recovery**: Mechanisms for clearing error flags and resuming operations
- **Graceful Degradation**: Continued operation of non-affected drums when one drum encounters an error

### Error Recovery Methods

The job manager provides specific methods for error recovery:

```python
async def clear_all_error_flags(self) -> bool:
    """
    Clear all error flags using unified error handling pattern.

    This method coordinates between job-level and OPC UA-level error clearing
    to ensure consistent error state management across the system.

    Returns:
        True if all error flags were cleared successfully
    """
    try:
        # Clear job-specific error flags
        job_cleared = await self.clear_job_error_flags()

        # Clear OPC UA error flags (backend_error, plc_error)
        opcua_cleared = await self.clear_error_flags()

        success = job_cleared and opcua_cleared
        if success:
            logger.info("All error flags cleared successfully")
        else:
            logger.warning("Some error flags failed to clear")
        return success

    except Exception as e:
        logger.error(f"Failed to clear all error flags: {e}")
        return False
```

This method allows operators to clear error conditions after addressing the underlying issue, enabling the job to resume from its current state rather than requiring a complete restart.

## Configuration and Validation

The Multilayer Job Manager implements several configuration options and validation rules to ensure proper job setup and execution.

### Configuration Management

Configuration is now centralized in the `JobConfig` class, which loads values from environment variables:

```python
@dataclass
class JobConfig:
    """Strongly-typed job configuration loaded from environment variables.

    All time-based values are in seconds.
    """
    MAX_DRUMS: int
    DRUM_UPLOAD_DELAY_SECONDS: float
    STATUS_POLL_INTERVAL_SECONDS: float
    READY_TIMEOUT_SECONDS: float
    COMPLETION_TIMEOUT_SECONDS: float
    LAYER_COMPLETION_QUERY_INTERVAL_SECONDS: float
    EMPTY_LAYER_TEMPLATE_PATH: str
    CACHE_CLEANUP_MAX_AGE_SECONDS: int


def get_job_config() -> JobConfig:
    """Load job configuration from environment with safe defaults.

    Returns:
        JobConfig: configuration object with explicit, engineer-friendly names.
    """
    # Use provided absolute path as default for maximum clarity, overrideable via .env
    default_template_path = os.path.abspath(
        os.getenv(
            "JOB_EMPTY_LAYER_TEMPLATE_PATH",
            r"c:\\Users\\<USER>\\Downloads\\SIMTech_Internship\\RecoaterSearch\\APIRecoater_Ethernet\\backend\\app\\templates\\empty_layer.cli",
        )
    )

    return JobConfig(
        MAX_DRUMS=int(os.getenv("JOB_MAX_DRUMS", "3")),
        DRUM_UPLOAD_DELAY_SECONDS=float(os.getenv("JOB_DRUM_UPLOAD_DELAY_SECONDS", "2.0")),
        STATUS_POLL_INTERVAL_SECONDS=float(os.getenv("JOB_STATUS_POLL_INTERVAL_SECONDS", "2.0")),
        READY_TIMEOUT_SECONDS=float(os.getenv("JOB_READY_TIMEOUT_SECONDS", "30.0")),
        COMPLETION_TIMEOUT_SECONDS=float(os.getenv("JOB_COMPLETION_TIMEOUT_SECONDS", "300.0")),
        LAYER_COMPLETION_QUERY_INTERVAL_SECONDS=float(os.getenv("LAYER_COMPLETION_QUERY_INTERVAL_SECONDS", "5.0")),
        EMPTY_LAYER_TEMPLATE_PATH=default_template_path,
        CACHE_CLEANUP_MAX_AGE_SECONDS=int(os.getenv("JOB_CACHE_CLEANUP_MAX_AGE_SECONDS", "3600")),
    )
```

These configuration values are defined in the `.env` file:

```
# Job Orchestration Settings (all values in seconds unless noted)
JOB_MAX_DRUMS=3
JOB_DRUM_UPLOAD_DELAY_SECONDS=2.0
JOB_STATUS_POLL_INTERVAL_SECONDS=2.0
JOB_READY_TIMEOUT_SECONDS=30.0
JOB_COMPLETION_TIMEOUT_SECONDS=300.0
JOB_EMPTY_LAYER_TEMPLATE_PATH=backend\app\templates\empty_layer.cli
```

### Validation Rules

The system implements validation rules through the mixins:

```python
def cache_cli_file_for_drum(self, drum_id: int, parsed_file: ParsedCliFile, filename: str) -> None:
    """Cache CLI file for specific drum in printing workflow.

    Args:
        drum_id: Drum identifier (0, 1, or 2)
        parsed_file: Parsed CLI file data
        filename: Original filename

    Raises:
        ValueError: If drum_id is invalid
    """
    if drum_id not in range(self.job_config.MAX_DRUMS):
        raise ValueError(f"Invalid drum_id: {drum_id}. Must be 0..{self.job_config.MAX_DRUMS-1}")

    self.drum_cli_cache[drum_id] = {
        'parsed_file': parsed_file,
        'filename': filename,
        'layer_count': len(parsed_file.layers)
    }
    logger.info(f"Cached CLI file '{filename}' for drum {drum_id} with {len(parsed_file.layers)} layers")
```

These validation rules ensure that jobs are created with valid configurations, preventing common setup errors.

## Performance Considerations

The Multilayer Job Manager is designed with performance considerations to efficiently manage large multi-layer print jobs.

### Memory-Efficient State Representation

The job manager uses a memory-efficient state representation by storing only essential information:

```python
class MultiMaterialJobService(LayerProcessingMixin, OPCUACoordinationMixin, CliCachingMixin):
    """
    Unified service that provides a cohesive interface for multi-material jobs.

    This concrete class inherits consolidated mixins that provide:
    - LayerProcessingMixin: Job lifecycle and layer upload operations
    - OPCUACoordinationMixin: OPC UA variable management and coordination
    - CliCachingMixin: CLI file caching for both generic and drum-specific operations
    """

    def __init__(self, recoater_client: RecoaterClient, opcua=None, job_config: Optional[JobConfig] = None):
        """
        Initialize the multi-material job service.

        Args:
            recoater_client: Hardware client for drum uploads and print control
            opcua: Injected OPC UA service instance (for testability and DI)
            job_config: Injected JobConfig (env-driven); if None, will be loaded
        """
        self.recoater_client = recoater_client
        self.opcua = opcua  # Injected OPC UA service (may be None)
        self.job_config = job_config or get_job_config()

        # Initialize state fields required by mixins
        self.cli_parser = __import__('infrastructure.cli_editor.editor', fromlist=['Editor']).Editor()
        self.current_job: Optional[MultiMaterialJobState] = None
        self.cli_cache: Dict[str, CliCacheEntry] = {}
        self.drum_cli_cache: Dict[int, Dict[str, Any]] = {i: None for i in range(self.job_config.MAX_DRUMS)}

        # Configuration from job config
        self.drum_upload_delay = self.job_config.DRUM_UPLOAD_DELAY_SECONDS
        self.status_poll_interval = self.job_config.LAYER_COMPLETION_QUERY_INTERVAL_SECONDS
```

This approach minimizes memory overhead while providing comprehensive state tracking through the mixin architecture.

### Large Job Management

For managing large jobs with thousands of layers, the system employs several strategies:

1. **Lazy Loading**: CLI data is only loaded when needed for upload, rather than keeping all layer data in memory
2. **Efficient Data Structures**: Using dictionaries and lists for O(1) access to drum and layer data
3. **Asynchronous Operations**: All I/O operations are performed asynchronously to prevent blocking
4. **Batch Processing**: Layer data is processed in batches to minimize memory pressure
5. **Configuration-Driven Delays**: The drum upload delay is configurable to prevent server overload during large job execution

## Testing and Validation

The Multilayer Job Manager is thoroughly tested to ensure reliability and correctness in production environments.

### Test Coverage

The test suite covers various scenarios including:

```
flowchart TD
TestSuite[MultiMaterialJobService Test Suite] --> Initialization["Test Initialization"]
TestSuite --> CLIFileHandling["Test CLI File Handling"]
TestSuite --> JobCreation["Test Job Creation"]
TestSuite --> JobExecution["Test Job Execution"]
TestSuite --> ErrorHandling["Test Error Handling"]
JobCreation --> SingleMaterial["Test Single Material Job"]
JobCreation --> DualMaterial["Test Dual Material Job"]
JobCreation --> TripleMaterial["Test Triple Material Job"]
JobCreation --> InvalidInput["Test Invalid Input"]
JobExecution --> StartJob["Test Start Job"]
JobExecution --> CancelJob["Test Cancel Job"]
JobExecution --> StatusReporting["Test Status Reporting"]
ErrorHandling --> MissingFiles["Test Missing CLI Files"]
ErrorHandling --> InvalidLayers["Test Invalid Layer Numbers"]
ErrorHandling --> UploadFailures["Test Upload Failures"]
```

**Diagram sources**
- [test_multilayer_job_manager.py](file://backend\tests\test_multilayer_job_manager.py#L1-L273)

**Section sources**
- [test_multilayer_job_manager.py](file://backend\tests\test_multilayer_job_manager.py#L1-L273)

### Key Test Cases

The test suite includes several key test cases that validate critical functionality:

**Drum Caching Validation**
```python
async def test_cache_cli_file_for_drum_valid(self, job_service, sample_cli_file):
    """Test successful caching of CLI file for a specific drum."""
    # Cache file for drum 0
    job_service.cache_cli_file_for_drum(0, sample_cli_file, "test_file.cli")
    
    # Verify cache contains the file
    cached = job_service.get_cached_file_for_drum(0)
    assert cached is not None
    assert cached['filename'] == "test_file.cli"
    assert cached['layer_count'] == len(sample_cli_file.layers)
```

This test verifies that the drum-specific caching system correctly stores and retrieves CLI files.

**Job Execution Flow**
```python
async def test_start_layer_by_layer_job_success(self, job_service, sample_cli_files):
    """Test successful execution of layer-by-layer job."""
    # Cache files for drums 0 and 1
    job_service.cache_cli_file_for_drum(0, sample_cli_files[0], "file_0.cli")
    job_service.cache_cli_file_for_drum(1, sample_cli_files[1], "file_1.cli")
    
    # Start job
    result = await job_service.start_layer_by_layer_job()
    
    assert result is True
    assert job_service.current_job is not None
    assert job_service.current_job.status == JobStatus.RUNNING
    assert job_service.current_job.total_layers == max(len(f.layers) for f in sample_cli_files.values())
```

This test verifies the complete job execution flow, from drum caching to successful job completion, ensuring that all state transitions occur correctly.

The comprehensive test suite ensures that the Multilayer Job Manager operates reliably under various conditions, providing confidence in its production deployment.