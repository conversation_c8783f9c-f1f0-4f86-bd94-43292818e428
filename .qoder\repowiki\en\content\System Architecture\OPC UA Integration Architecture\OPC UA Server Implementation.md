# OPC UA Server Implementation

<cite>
**Referenced Files in This Document**   
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py) - *Updated in recent commit*
- [opcua_config.py](file://backend/app/config/opcua_config.py) - *Updated in recent commit*
- [models.py](file://backend/app/services/opcua/models.py) - *Added in recent commit*
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py) - *Added in recent commit*
- [coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py) - *Added in recent commit*
- [monitoring_mixin.py](file://backend/app/services/opcua/mixins/monitoring_mixin.py) - *Added in recent commit*
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [test_opcua_infrastructure.py](file://backend/tests/test_opcua_infrastructure.py)
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect the refactored OPC UA service implementation using mixin-based architecture
- Added new sections for the OPCUAService class and its mixins
- Removed references to deprecated CoordinationStatus enum and OPCUA exceptions
- Updated configuration details to reflect centralized configuration in .env file
- Added new diagrams for the mixin-based architecture and inheritance order
- Updated code examples to reflect current implementation

## Table of Contents
1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Core Components](#core-components)
4. [Architecture Overview](#architecture-overview)
5. [Detailed Component Analysis](#detailed-component-analysis)
6. [Dependency Analysis](#dependency-analysis)
7. [Performance Considerations](#performance-considerations)
8. [Troubleshooting Guide](#troubleshooting-guide)
9. [Conclusion](#conclusion)

## Introduction
This document provides a comprehensive analysis of the OPC UA server implementation in the APIRecoater_Ethernet project. The system enables industrial communication between a FastAPI backend and a TwinCAT PLC through OPC UA protocol, facilitating coordination in multi-material 3D printing processes. The implementation uses the FreeOpcUa (asyncua) library to expose backend state variables such as print job status, axis positions, and recoater controls to external SCADA systems. This documentation details the architecture, configuration, data model, and integration patterns while maintaining accessibility for users with varying technical backgrounds.

## Project Structure
The project follows a layered architecture with clear separation between configuration, services, API endpoints, and infrastructure components. The OPC UA functionality is primarily contained within the backend/app/services/opcua directory, with integration into the main FastAPI application through dependency injection.

```mermaid
graph TD
subgraph "Backend"
A[main.py] --> B[dependencies.py]
B --> C[opcua_service.py]
C --> D[models.py]
C --> E[mixins]
E --> F[server_mixin.py]
E --> G[coordination_mixin.py]
E --> H[monitoring_mixin.py]
C --> I[opcua_config.py]
end
subgraph "Configuration"
I --> J[Environment Variables]
end
subgraph "Testing"
K[test_opcua_infrastructure.py]
end
A --> L[API Routers]
L --> M[print.py]
L --> N[status.py]
L --> O[axis.py]
C --> P[Web Interface]
F --> Q[PLC Client]
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [models.py](file://backend/app/services/opcua/models.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

**Section sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)

## Core Components
The OPC UA implementation consists of three core components working in concert: the server manager (low-level protocol handling), the coordinator (high-level business logic), and the configuration system (settings and variable definitions). These components enable bidirectional communication between the Python backend and industrial PLC systems using standardized OPC UA protocols.

**Section sources**
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [models.py](file://backend/app/services/opcua/models.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Architecture Overview
The architecture implements a layered approach to OPC UA communication, abstracting protocol complexity from business logic. The design follows industrial interoperability standards (OPC UA Part 6) while providing a clean interface for integration with web APIs.

```mermaid
graph TB
subgraph "Frontend"
UI[Web Interface]
end
subgraph "Backend Application"
API[FastAPI Server]
DEP[Dependency Manager]
WS[WebSocket Manager]
end
subgraph "OPC UA Layer"
SERVICE[OPCUAService]
SERVER[ServerMixin]
COORD[CoordinationMixin]
MONITOR[MonitoringMixin]
CONFIG[opcua_config]
end
subgraph "Industrial Network"
PLC[TwinCAT PLC]
SCADA[SCADA System]
end
UI --> API
API --> DEP
DEP --> SERVICE
SERVICE --> SERVER
SERVICE --> COORD
SERVICE --> MONITOR
SERVICE --> CONFIG
SERVER < --> PLC
SERVER < --> SCADA
style SERVICE fill:#f9f,stroke:#333
style SERVER fill:#bbf,stroke:#333
style COORD fill:#f96,stroke:#333
style MONITOR fill:#9f9,stroke:#333
style CONFIG fill:#dfd,stroke:#333
```

**Diagram sources**
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [models.py](file://backend/app/services/opcua/models.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Detailed Component Analysis

### OPCUAService Analysis
The OPCUAService class provides a unified interface for OPC UA operations by combining server management, coordination, and monitoring functionality through mixin composition.

#### Class Diagram
```mermaid
classDiagram
class OPCUAService {
-config : ServerConfig
-_logger : Logger
-_server_running : bool
-_opcua_server : Optional[Server]
-_variable_nodes : Dict[str, Node]
-_variable_cache : Dict[str, Any]
-_connected : bool
-_monitoring_task : Optional[Task]
-monitoring_active : bool
-_event_handlers : Dict[str, List]
+__init__(config)
+initialize() bool
+shutdown() bool
+get_variable(name) Any
+get_job_active() bool
+get_total_layers() int
+get_current_layer() int
+get_backend_error() bool
+get_plc_error() bool
+get_recoater_ready_to_print() bool
+get_recoater_layer_complete() bool
+get_server_status() dict
+__aenter__()
+__aexit__(exc_type, exc_val, exc_tb)
}
class ServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+certificate_path : str
+private_key_path : str
+connection_timeout : float
+session_timeout : float
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
class VariableDefinition {
+name : str
+node_id : str
+data_type : str
+initial_value : Any
+writable : bool
+description : str
}
OPCUAService --> ServerConfig : "uses"
OPCUAService --> VariableDefinition : "manages"
```

**Diagram sources**
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L81-L194)
- [models.py](file://backend/app/services/opcua/models.py#L1-L65)

#### Inheritance Order Diagram
```mermaid
graph TD
A[CoordinationMixin] --> B[ServerMixin]
B --> C[MonitoringMixin]
C --> D[OPCUAService]
style A fill:#f96,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#9f9,stroke:#333
style D fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L81-L194)

#### Service Initialization Sequence
```mermaid
sequenceDiagram
participant App as Application
participant Service as OPCUAService
participant Config as opcua_config
participant Mixins as Mixins
App->>Service : initialize()
Service->>Config : get_opcua_config()
Config-->>Service : OPCUAServerConfig
Service->>Service : Convert to ServerConfig
Service->>Mixins : Initialize state
Mixins-->>Service : Initialize mixins
Service->>Service : super().__init__()
Service-->>App : True
Note over Service,Config : Configuration loaded from environment variables
```

**Diagram sources**
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L81-L194)
- [opcua_config.py](file://backend/app/config/opcua_config.py#L134-L175)

### ServerMixin Analysis
The ServerMixin provides low-level management of the OPC UA server instance, handling protocol-specific operations and server lifecycle management.

#### Class Diagram
```mermaid
classDiagram
class ServerMixin {
-_logger : Logger
-_server_running : bool
-_opcua_server : Optional[Server]
-_variable_nodes : Dict[str, Node]
-_variable_cache : Dict[str, Any]
+_get_server_config() OPCUAServerConfig
+_get_ua_variant_type(data_type) ua.VariantType
+_create_server_variables() bool
+start_server() bool
+stop_server() bool
+is_server_running : bool
+get_variable_names() List[str]
+_convert_value_to_correct_type(name, value) Any
+write_variable(name, value) bool
+read_variable(name) Any
}
class OPCUAServerConfig {
+endpoint : str
+server_name : str
+namespace_uri : str
+namespace_idx : int
+security_policy : str
+security_mode : str
+certificate_path : str
+private_key_path : str
+connection_timeout : float
+session_timeout : float
+auto_restart : bool
+restart_delay : float
+max_restart_attempts : int
}
class CoordinationVariable {
+name : str
+node_id : str
+data_type : str
+initial_value : Any
+writable : bool
+description : str
}
ServerMixin --> OPCUAServerConfig : "uses"
ServerMixin --> CoordinationVariable : "manages"
```

**Diagram sources**
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L1-L291)
- [opcua_config.py](file://backend/app/config/opcua_config.py#L34-L78)

#### Server Startup Sequence
```mermaid
sequenceDiagram
participant App as Application
participant Service as OPCUAService
participant Server as ServerMixin
participant UA as asyncua.Server
App->>Service : start_server()
Service->>Server : start_server()
Server->>Server : _get_server_config()
Server->>UA : Server()
Server->>UA : init()
Server->>UA : set_endpoint()
Server->>UA : set_server_name()
Server->>UA : register_namespace()
Server->>Server : _create_server_variables()
Server->>UA : start()
Server-->>Service : True
Service-->>App : True
Note over Server,UA : Server successfully started on opc.tcp : //0.0.0.0 : 4843
```

**Diagram sources**
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L208-L250)
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py#L81-L194)

### CoordinationMixin Analysis
The CoordinationMixin provides a high-level interface that abstracts the complexity of OPC UA protocol operations, offering business-oriented methods for job coordination and status management.

#### Class Diagram
```mermaid
classDiagram
class CoordinationMixin {
-_logger : Logger
-_connected : bool
-_monitoring_task : Optional[Task]
-_event_handlers : Dict[str, List]
+connect() bool
+disconnect() bool
+is_connected() bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+_trigger_event_handlers(name, value) None
}
CoordinationMixin --> ServerMixin : "delegates to"
CoordinationMixin --> OPCUAServerConfig : "uses"
```

**Diagram sources**
- [coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py#L1-L182)
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py)

#### Job Activation Flow
```mermaid
sequenceDiagram
participant API as Print API
participant Coord as CoordinationMixin
participant Server as ServerMixin
participant PLC as TwinCAT PLC
API->>Coord : set_job_active(total_layers=150)
Coord->>Server : write_variable("job_active", True)
Coord->>Server : write_variable("total_layers", 150)
Coord->>Server : write_variable("current_layer", 1)
Server-->>Coord : Success
Coord-->>API : True
PLC->>Server : Read job_active
PLC->>Server : Read total_layers
PLC->>Server : Read current_layer
Note over Coord,Server : Three coordination variables updated atomically
```

**Diagram sources**
- [coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py#L358-L373)
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L388-L408)

### MonitoringMixin Analysis
The MonitoringMixin provides health monitoring and system reliability features for OPC UA operations.

#### Class Diagram
```mermaid
classDiagram
class MonitoringMixin {
-monitoring_active : bool
-_monitor_task : Optional[Task]
-_logger : Logger
+start_monitoring(interval) None
+stop_monitoring() None
+_monitoring_loop(interval) None
}
```

**Diagram sources**
- [monitoring_mixin.py](file://backend/app/services/opcua/mixins/monitoring_mixin.py#L1-L84)

### Configuration System Analysis
The configuration system provides a flexible way to define OPC UA server settings and coordination variables, supporting both default values and environment variable overrides.

#### Data Model
```mermaid
erDiagram
OPCUAServerConfig {
string endpoint PK
string server_name
string namespace_uri
int namespace_idx
string security_policy
string security_mode
bool auto_restart
float restart_delay
int max_restart_attempts
}
CoordinationVariable {
string name PK
string node_id
string data_type
any initial_value
bool writable
string description
}
OPCUAServerConfig ||--o{ CoordinationVariable : "defines"
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L34-L232)

#### Configuration Flow
```mermaid
flowchart TD
Start([Application Start]) --> LoadEnv["Load Environment Variables"]
LoadEnv --> CreateConfig["Create OPCUAServerConfig"]
CreateConfig --> SetDefaults["Apply Default Values"]
SetDefaults --> Override["Override with Environment Variables"]
Override --> Validate["Validate Configuration"]
Validate --> CreateVars["Create COORDINATION_VARIABLES"]
CreateVars --> Expose["Expose via opcua_config"]
Expose --> End([Configuration Ready])
style Start fill:#f9f,stroke:#333
style End fill:#f9f,stroke:#333
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L134-L175)

## Dependency Analysis
The OPC UA components are integrated into the application through a dependency injection pattern, ensuring proper initialization order and resource management.

```mermaid
graph TD
main[main.py] --> dependencies[dependencies.py]
dependencies --> service[opcua_service.py]
service --> server[server_mixin.py]
service --> coordination[coordination_mixin.py]
service --> monitoring[monitoring_mixin.py]
service --> config[opcua_config.py]
server --> asyncua[asyncua library]
coordination --> logger[logging]
server --> logger
style main fill:#f96,stroke:#333
style dependencies fill:#69f,stroke:#333
style service fill:#f9f,stroke:#333
style server fill:#bbf,stroke:#333
style coordination fill:#f96,stroke:#333
style monitoring fill:#9f9,stroke:#333
style config fill:#dfd,stroke:#333
```

**Diagram sources**
- [main.py](file://backend/app/main.py)
- [dependencies.py](file://backend/app/dependencies.py)
- [opcua_service.py](file://backend/app/services/opcua/opcua_service.py)
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py)
- [coordination_mixin.py](file://backend/app/services/opcua/mixins/coordination_mixin.py)
- [monitoring_mixin.py](file://backend/app/services/opcua/mixins/monitoring_mixin.py)
- [opcua_config.py](file://backend/app/config/opcua_config.py)

## Performance Considerations
The OPC UA implementation includes several performance and reliability features designed for industrial environments:

- **Heartbeat Mechanism**: The `_monitoring_loop()` method runs every 2 seconds to maintain server responsiveness and detect failures early.
- **Error Recovery**: Automatic server restart capability with configurable attempts (default: 3) and delay (default: 5 seconds).
- **Type Coercion**: Efficient value type conversion prevents BadTypeMismatch errors during variable writes.
- **Asynchronous Operations**: All methods use async/await pattern to prevent blocking the event loop.
- **Graceful Shutdown**: Proper cleanup of resources including monitoring tasks and server instances.
- **Variable Caching**: The `_variable_cache` dictionary provides synchronous access to variable values for improved performance.

The system is optimized for moderate-frequency data publishing (typically 1-10 Hz for industrial processes) rather than high-frequency sensor data. For higher frequency requirements, OPC UA subscriptions would need to be implemented in the monitoring loop.

**Section sources**
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L488-L500)
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L475-L486)

## Troubleshooting Guide
Common issues and their solutions for the OPC UA implementation:

### Server Startup Failures
**Symptom**: Server fails to start with "Failed to start OPC UA server" error
**Causes**:
- Port 4843 already in use
- Invalid security configuration
- Network interface binding issues

**Solution**: Check port availability and verify endpoint configuration in environment variables.

### Variable Access Errors
**Symptom**: "Variable not found" when reading/writing
**Causes**:
- Variable name mismatch
- Server not fully initialized
- Case sensitivity issues

**Solution**: Verify variable names against COORDINATION_VARIABLES list and ensure server is running.

### Connection Timeouts
**Symptom**: PLC cannot connect to OPC UA server
**Causes**:
- Firewall blocking port 4843
- Incorrect IP address in endpoint URL
- Security policy mismatch

**Solution**: Use `opc.tcp://0.0.0.0:4843` for development and verify network connectivity.

### Type Mismatch Errors
**Symptom**: BadTypeMismatch during variable writes
**Causes**:
- Sending integer when Boolean expected
- Null values for non-nullable types

**Solution**: Ensure proper type coercion using the built-in type mapping system.

**Section sources**
- [server_mixin.py](file://backend/app/services/opcua/mixins/server_mixin.py#L409-L450)
- [test_opcua_infrastructure.py](file://backend/tests/test_opcua_infrastructure.py#L108-L128)

## Conclusion
The OPC UA server implementation in APIRecoater_Ethernet provides a robust, standards-compliant interface for industrial communication between a Python backend and PLC systems. By following OPC UA Part 6 standards, the implementation ensures interoperability with SCADA systems and industrial automation equipment. The layered architecture separates concerns effectively, with the ServerMixin handling protocol details, the CoordinationMixin providing business logic, and the configuration system enabling flexibility. The integration with FastAPI through dependency injection creates a cohesive system where REST API endpoints can update OPC UA variables that are immediately available to PLC clients. This design supports reliable, real-time coordination of multi-material 3D printing processes in industrial environments.