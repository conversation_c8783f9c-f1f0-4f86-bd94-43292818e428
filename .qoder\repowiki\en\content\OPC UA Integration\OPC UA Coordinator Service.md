# OPC UA Coordinator Service

<cite>
**Referenced Files in This Document**   
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py) - *Updated in commit 4f0a456*
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py) - *Refactored in commit 4f0a456*
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py) - *Implemented in commit e14f3fe*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Updated in commit e14f3fe*
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py) - *Updated in commit e14f3fe*
</cite>

## Update Summary
**Changes Made**   
- Updated service architecture to reflect mixin-based composition in OPCUAService
- Revised core responsibilities to align with 7-variable OPC UA architecture
- Added detailed explanation of coordination workflow in job management
- Updated code examples to reflect async methods and proper error handling
- Enhanced troubleshooting guidance with new error recovery patterns
- Added new section on integration with MultiMaterialJobService
- Removed outdated references to legacy time fields and deprecated methods

## Table of Contents
1. [Introduction](#introduction)
2. [Core Responsibilities](#core-responsibilities)
3. [Architecture Overview](#architecture-overview)
4. [Connection Management](#connection-management)
5. [Variable Coordination and State Synchronization](#variable-coordination-and-state-synchronization)
6. [Event-Driven Architecture with WebsocketManager](#event-driven-architecture-with-websocketmanager)
7. [Command Routing and API Integration](#command-routing-and-api-integration)
8. [Error Handling and Resilience](#error-handling-and-resilience)
9. [Performance and Thread Safety](#performance-and-thread-safety)
10. [Troubleshooting Guide](#troubleshooting-guide)
11. [Integration with MultiMaterialJobService](#integration-with-multimaterialjobservice)

## Introduction

The OPCUACoordinator service, implemented as OPCUAService, is the central orchestrator for all OPC UA interactions within the APIRecoater_Ethernet system. It acts as a high-level abstraction layer between the application logic and the underlying OPC UA communication infrastructure, managing session lifecycle, variable subscriptions, and command routing. This service ensures synchronized state changes across multiple components by interfacing with services such as StatusPoller and RecoaterControlsAPI. The coordinator implements robust reconnection logic, error handling, and event broadcasting via WebsocketManager to support real-time monitoring and control of the recoater system.

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)

## Core Responsibilities

The OPCUAService manages seven key coordination variables used in multi-material print jobs:
- **job_active**: Indicates whether a print job is currently active
- **total_layers**: Total number of layers in the current job
- **current_layer**: Current layer being processed
- **recoater_ready_to_print**: Status indicating readiness for printing
- **recoater_layer_complete**: Flag set when layer deposition is complete
- **backend_error**: Error flag for backend system issues
- **plc_error**: Error flag for PLC communication problems

These variables are synchronized between the backend and the PLC to ensure consistent state across the system.

```mermaid
classDiagram
class OPCUAService {
-config : ServerConfig
-_logger : Logger
-_server_running : bool
-_opcua_server : Server
-_variable_nodes : Dict[str, Node]
-_variable_cache : Dict[str, Any]
-_connected : bool
-_monitoring_task : Task
-monitoring_active : bool
-_event_handlers : Dict[str, List[Callable]]
+initialize() bool
+shutdown() bool
+connect() bool
+disconnect() bool
+is_connected() bool
+start_server() bool
+stop_server() bool
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+get_backend_error() bool
+get_plc_error() bool
+get_server_status() Dict[str, Any]
}
class CoordinationMixin {
+connect() bool
+disconnect() bool
+is_connected() bool
+set_job_active(total_layers) bool
+set_job_inactive() bool
+update_layer_progress(current_layer) bool
+set_recoater_ready_to_print(ready) bool
+set_recoater_layer_complete(complete) bool
+set_backend_error(error) bool
+set_plc_error(error) bool
+clear_error_flags() bool
+write_variable(name, value) bool
+read_variable(name) Any
+subscribe_to_changes(variables, handler) bool
+_trigger_event_handlers(name, value) None
}
class ServerMixin {
+start_server() bool
+stop_server() bool
+is_server_running : bool
+get_variable_names() List[str]
+write_variable(name, value) bool
+read_variable(name) Any
+_create_server_variables() bool
+_get_ua_variant_type(data_type) VariantType
}
class MonitoringMixin {
+start_monitoring(interval) bool
+stop_monitoring() bool
+_monitoring_loop() None
}
OPCUAService --|> CoordinationMixin
OPCUAService --|> ServerMixin
OPCUAService --|> MonitoringMixin
```

**Diagram sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py#L1-L292)

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)

## Architecture Overview

The OPCUAService integrates with multiple system components to provide a unified interface for OPC UA operations. It leverages a mixin-based architecture combining CoordinationMixin, ServerMixin, and MonitoringMixin to provide a cohesive interface. The service uses WebsocketManager to broadcast state changes to connected clients and is accessed through REST APIs exposed by RecoaterControlsAPI, enabling web-based control and monitoring.

```mermaid
graph TB
subgraph "Frontend"
UI[Web Interface]
WSClient[WebSocket Client]
end
subgraph "Backend"
API[RecoaterControlsAPI]
OPCUA[OPCUAService]
Coordination[CoordinationMixin]
Server[ServerMixin]
Monitoring[MonitoringMixin]
WSManager[WebsocketManager]
end
UI --> API
API --> OPCUA
OPCUA --> Coordination
OPCUA --> Server
OPCUA --> Monitoring
OPCUA --> WSManager
WSClient --> WSManager
WSManager --> WSClient
style OPCUA fill:#f9f,stroke:#333
style WSManager fill:#bbf,stroke:#333
```

**Diagram sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [websocket_manager.py](file://backend\app\services\communication\websocket_manager.py#L0-L146)
- [recoater_controls.py](file://backend\app\api\recoater_controls\__init__.py#L0-L799)

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [websocket_manager.py](file://backend\app\services\communication\websocket_manager.py#L0-L146)
- [recoater_controls.py](file://backend\app\api\recoater_controls\__init__.py#L0-L799)

## Connection Management

The OPCUAService manages its connection lifecycle through explicit connect() and disconnect() methods. When initialized, it references a configuration object and composes functionality from CoordinationMixin, ServerMixin, and MonitoringMixin.

### Connection Flow
```mermaid
sequenceDiagram
participant Client
participant Service as OPCUAService
participant Coordination as CoordinationMixin
participant Server as ServerMixin
Client->>Service : initialize()
Service->>Server : start_server()
Server-->>Service : Server started
Service->>Coordination : connect()
Coordination->>Coordination : Check _connected flag
alt Already connected
Coordination-->>Service : Return True
else Not connected
Coordination->>Coordination : Set _connected = True
Coordination->>Coordination : Start monitoring
Coordination-->>Service : Return True
end
Service-->>Client : Return True
```

The connection process includes:
1. Starting the OPC UA server via ServerMixin
2. Establishing coordination connection via CoordinationMixin
3. Initializing the monitoring loop
4. Setting internal connection state

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)

## Variable Coordination and State Synchronization

The coordinator provides methods to read and write coordination variables, ensuring proper synchronization between the backend and PLC systems.

### Job State Management
```mermaid
flowchart TD
Start([set_job_active]) --> Write1["write_variable('job_active', True)"]
Write1 --> Write2["write_variable('total_layers', total_layers)"]
Write2 --> Write3["write_variable('current_layer', 1)"]
Write3 --> End([Return Success])
Start2([set_job_inactive]) --> Write4["write_variable('job_active', False)"]
Write4 --> Write5["write_variable('recoater_ready_to_print', False)"]
Write5 --> Write6["write_variable('recoater_layer_complete', False)"]
Write6 --> Write7["write_variable('backend_error', False)"]
Write7 --> Write8["write_variable('plc_error', False)"]
Write8 --> End2([Return Success])
```

The service also supports subscription to variable changes, allowing other components to register event handlers that are triggered when specific variables change value.

**Section sources**
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py#L1-L292)

## Event-Driven Architecture with WebsocketManager

The OPCUAService works in conjunction with WebsocketManager to enable real-time updates to connected clients. While the coordinator itself does not directly call broadcast(), it triggers events that propagate through the system to generate WebSocket messages.

### WebSocket Integration
```mermaid
sequenceDiagram
participant Frontend
participant Backend
participant WSManager as WebsocketManager
Frontend->>Backend : Connect to /ws
Backend->>WSManager : connect(websocket)
WSManager-->>Backend : Accept connection
Backend-->>Frontend : Connection established
loop Periodic Updates
Backend->>WSManager : broadcast(message)
WSManager->>WSManager : _filter_message_for_connection()
WSManager->>Frontend : Send filtered message
end
Frontend->>Backend : Send subscription update
Backend->>WSManager : update_subscription(websocket, data_types)
WSManager-->>Backend : Subscription updated
```

The WebsocketManager maintains:
- **active_connections**: List of active WebSocket connections
- **connection_subscriptions**: Mapping of connections to their subscribed data types
- **broadcast()**: Method to send messages to all connected clients
- **_filter_message_for_connection()**: Filters messages based on subscription preferences

Clients can subscribe to different data types including 'status', 'axis', 'drum', 'leveler', and 'print' data.

**Diagram sources**
- [websocket_manager.py](file://backend\app\services\communication\websocket_manager.py#L0-L146)
- [main.py](file://backend\app\main.py#L105-L138)

**Section sources**
- [websocket_manager.py](file://backend\app\services\communication\websocket_manager.py#L0-L146)
- [main.py](file://backend\app\main.py#L105-L138)

## Command Routing and API Integration

The OPCUAService is accessed through the RecoaterControlsAPI, which exposes REST endpoints for various control operations. API requests are translated into OPC UA method calls via the coordinator.

### API to OPC UA Flow
```mermaid
sequenceDiagram
participant Client
participant API as RecoaterControlsAPI
participant Service as OPCUAService
participant Coordination as CoordinationMixin
Client->>API : POST /recoater/drum/0/motion
API->>API : Validate request (DrumMotionRequest)
API->>Service : set_drum_motion(drum_id, mode, speed, distance)
Service->>Coordination : write_variable("drum0_speed", speed)
Coordination-->>Service : Success
Service->>Service : _trigger_event_handlers()
Service-->>API : Response
API-->>Client : JSON response with status
```

Example code showing how an API request triggers OPC UA operations:

```python
@router.post("/drums/{drum_id}/motion")
async def set_drum_motion(
    motion_request: DrumMotionRequest,
    drum_id: int,
    client: RecoaterClient = Depends(get_recoater_client)
):
    # This call ultimately routes through OPCUAService
    response_data = client.set_drum_motion(
        drum_id=drum_id,
        mode=motion_request.mode,
        speed=motion_request.speed,
        distance=motion_request.distance,
        turns=motion_request.turns
    )
    return response
```

**Section sources**
- [recoater_controls.py](file://backend\app\api\recoater_controls\__init__.py#L0-L799)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)

## Error Handling and Resilience

The coordinator implements comprehensive error handling strategies to ensure system reliability.

### Error Management Methods
- **set_backend_error(error: bool)**: Sets the backend error flag and logs appropriately
- **set_plc_error(error: bool)**: Sets the PLC error flag and logs appropriately  
- **clear_error_flags()**: Clears both backend and PLC error flags
- Internal try-except blocks in all public methods to prevent unhandled exceptions

The service also handles connection-related errors gracefully:
- Returns False for write/read operations when not connected
- Logs warnings for attempted operations on disconnected coordinator
- Implements proper cleanup during disconnection (cancelling tasks, stopping server)

### Monitoring Loop Error Handling
```mermaid
flowchart TD
A[_monitoring_loop] --> B{connected?}
B --> |Yes| C[Wait 1 second]
C --> D{Cancelled?}
D --> |Yes| E[Log debug message]
D --> |No| F{Exception?}
F --> |Yes| G[Log error]
F --> |No| A
B --> |No| H[Exit loop]
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)
- [server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py#L1-L292)

## Performance and Thread Safety

The OPCUAService is designed with performance and concurrency in mind:

### Subscription Management
- Uses a dictionary of lists (`_event_handlers`) to store multiple handlers per variable
- Efficient O(1) lookup for event handler retrieval
- Asynchronous execution of event handlers to prevent blocking

### Connection Pooling Considerations
While the current implementation does not use traditional connection pooling, it employs a singleton-like pattern through the shared OPCUAService instance, ensuring only one server connection is maintained.

### Thread Safety Analysis
- The service is primarily async-cooperative rather than multi-threaded
- All operations are awaitable coroutines
- The _monitoring_task is properly cancellable
- Event handler execution is isolated to prevent one failing handler from affecting others

Potential race conditions are mitigated by:
- Atomic connection state flag (_connected)
- Proper asyncio task cancellation
- Dictionary access protected by Python's GIL for basic operations

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py#L1-L183)

## Troubleshooting Guide

### Common Issues and Solutions

#### Session Expiration
**Symptoms**: Connection drops after period of inactivity  
**Diagnosis**: Check server logs for disconnection messages  
**Solution**: Ensure heartbeat mechanism is active; verify server keep-alive settings

#### Subscription Loss
**Symptoms**: Clients stop receiving updates  
**Diagnosis**: Verify subscription state in WebsocketManager.connection_subscriptions  
**Solution**: Implement re-subscription logic in client; check for WebSocket disconnections

#### Race Conditions
**Symptoms**: Inconsistent variable states, missed events  
**Prevention**: 
- Ensure all coordinator methods are called asynchronously
- Avoid parallel calls to set_job_active/set_job_inactive
- Use clear_error_flags() instead of individual flag resets when possible

#### Connection Failures
**Symptoms**: connect() returns False  
**Diagnosis**: 
- Check if OPC UA server is running
- Verify endpoint configuration
- Ensure port 8000 is not blocked

**Recovery Procedure**:
```python
# Example recovery sequence
await coordinator.disconnect()
# Wait briefly
await asyncio.sleep(2)
# Reconnect
success = await coordinator.connect()
if not success:
    logger.critical("Failed to recover OPC UA connection")
```

#### Debugging Tips
1. Enable detailed logging to trace coordinator operations
2. Monitor WebsocketManager.connection_count to verify client connections
3. Use get_server_status() to check coordinator health
4. Validate variable values with read_variable() after write operations

**Section sources**
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)
- [websocket_manager.py](file://backend\app\services\communication\websocket_manager.py#L0-L146)

## Integration with MultiMaterialJobService

The OPCUAService is tightly integrated with the MultiMaterialJobService through the OPCUACoordinationMixin, which provides the coordination logic for multi-material print jobs.

### Job Workflow Integration
```mermaid
flowchart TD
A[MultiMaterialJobService] --> B[setup_opcua_job]
B --> C[set_job_active(total_layers)]
C --> D[update_layer_progress(1)]
D --> E[Process Layers]
E --> F[reset_opcua_layer_flags]
F --> G[signal_opcua_ready_to_print]
G --> H[wait_for_layer_completion]
H --> I[signal_opcua_layer_complete]
I --> J[update_layer_progress]
J --> K{More Layers?}
K --> |Yes| E
K --> |No| L[cleanup_opcua_job]
L --> M[set_job_inactive]
```

The integration follows the 7-variable OPC UA architecture:
1. **job_active**: Set to True at job start, False at completion
2. **total_layers**: Set once at job initialization
3. **current_layer**: Updated after each layer completion
4. **recoater_ready_to_print**: Set to True before each layer print
5. **recoater_layer_complete**: Set to True after layer completion
6. **backend_error**: Set to True on any backend error
7. **plc_error**: Monitored from PLC status

Error handling is coordinated through:
- **handle_job_error()**: Sets backend_error flag on exceptions
- **wait_until_error_cleared()**: Blocks until both backend_error and plc_error are False
- **clear_error_flags()**: Clears both error flags when resolved

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L1-L333)
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L251)
- [opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L1-L201)