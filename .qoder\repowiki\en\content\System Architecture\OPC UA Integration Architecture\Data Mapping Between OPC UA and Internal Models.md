# Data Mapping Between OPC UA and Internal Models

<cite>
**Referenced Files in This Document**   
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py) - *Updated in recent commit*
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py) - *Updated in recent commit*
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py) - *Updated in recent commit*
- [opcua_config.py](file://backend\app\config\opcua_config.py) - *Configuration for OPC UA variables*
- [models.py](file://backend\app\services\opcua\models.py) - *Domain models for OPC UA operations*
- [multilayer_job.py](file://backend\app\models\multilayer_job.py) - *Internal domain model definitions*
- [OPCUA_SERVER_IMPLEMENTATION.md](file://OPCUA_SERVER_IMPLEMENTATION.md) - *Implementation details of real OPC UA server*
</cite>

## Update Summary
**Changes Made**   
- Updated **Introduction** to reflect the shift from in-memory state to OPC UA as the source of truth
- Revised **Internal Domain Models** section to clarify separation between OPC UA variables and in-memory job state
- Updated **Reading OPC UA Node Values into Internal State** to reflect the new async `get_job_status` implementation
- Modified **Schema Diagrams and Field Mappings** to accurately represent the 7-variable architecture
- Added new **7-Variable Architecture** section to document the core coordination mechanism
- Removed outdated **Synchronization Timing and Consistency** state diagram that no longer reflects current implementation
- Updated all relevant section sources to reflect the actual files analyzed

## Table of Contents
1. [Introduction](#introduction)
2. [7-Variable Architecture](#7-variable-architecture)
3. [Internal Domain Models](#internal-domain-models)
4. [Bidirectional Data Mapping Architecture](#bidirectional-data-mapping-architecture)
5. [Writing Internal State to OPC UA Nodes](#writing-internal-state-to-opc-ua-nodes)
6. [Reading OPC UA Node Values into Internal State](#reading-opc-ua-node-values-into-internal-state)
7. [Type Conversion and Value Coercion](#type-conversion-and-value-coercion)
8. [Error Handling and Null Value Management](#error-handling-and-null-value-management)
9. [Coordination Engine and Status Polling](#coordination-engine-and-status-polling)
10. [Schema Diagrams and Field Mappings](#schema-diagrams-and-field-mappings)

## Introduction
This document details the bidirectional data mapping system between OPC UA nodes and internal Python domain models in the multi-material recoater system. The architecture enables seamless coordination between the FastAPI backend and TwinCAT PLC through a well-defined set of coordination variables. The system uses OPC UA as a communication protocol to synchronize state between the backend application and industrial control systems, allowing real-time monitoring and control of the recoater process.

The data mapping system consists of two primary components: the OPC UA server that exposes coordination variables to the PLC, and the coordination layer that translates between internal application state and OPC UA variables. A significant recent change has converted the system to use OPC UA variables as the single source of truth for job status, replacing the previous reliance on in-memory state. The `get_job_status` method is now asynchronous and reads directly from 7 OPC UA variables (`job_active`, `total_layers`, `current_layer`, `recoater_ready_to_print`, `recoater_layer_complete`, `backend_error`, `plc_error`) to determine the current job status.

**Section sources**
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py#L159-L193) - *Updated get_job_status implementation*
- [OPCUA_SERVER_IMPLEMENTATION.md](file://OPCUA_SERVER_IMPLEMENTATION.md#L1-L118) - *Real OPC UA server implementation*

## 7-Variable Architecture
The system implements a 7-variable architecture for coordination between the backend and PLC. These variables serve as shared "mailboxes" for real-time communication without polling delays. The backend hosts these variables in its OPC UA server, while the PLC connects as a client to read and write them.

```mermaid
classDiagram
class VariableDefinition {
+string name
+string node_id
+string data_type
+Any initial_value
+bool writable
+string description
}
class CoordinationVariables {
- job_active : Boolean
- total_layers : Int32
- current_layer : Int32
- recoater_ready_to_print : Boolean
- recoater_layer_complete : Boolean
- backend_error : Boolean
- plc_error : Boolean
}
class CommunicationFlow {
Backend (OPC UA Server) ←→ PLC (OPC UA Client)
}
VariableDefinition --> CoordinationVariables : "defines"
CommunicationFlow .. CoordinationVariables : "uses"
```

**Diagram sources**
- [opcua_config.py](file://backend\app\config\opcua_config.py#L67-L107) - *Variable architecture documentation*
- [models.py](file://backend\app\services\opcua\models.py#L40-L64) - *VariableDefinition domain model*

## Internal Domain Models
The internal state of the application is represented by domain models that capture the complete state of multi-material print jobs. These models are synchronized with OPC UA variables to maintain consistency between the application and control systems. The `MultiMaterialJobState` model contains job-level information such as job ID, layer progress, and error status, while `DrumState` models track the state of individual drums.

```mermaid
classDiagram
class JobStatus {
<<enumeration>>
IDLE
INITIALIZING
WAITING_FOR_PRINT_START
RECOATER_ACTIVE
WAITING_FOR_DEPOSITION
LAYER_COMPLETE
JOB_COMPLETE
ERROR
CANCELLED
RUNNING
}
class DrumState {
+int drum_id
+string status
+bool ready
+bool uploaded
+int current_layer
+int total_layers
+string error_message
+string file_id
+float last_update_time
+reset() void
}
class LayerData {
+int layer_number
+bytes cli_data
+bool is_empty
+float upload_time
+float completion_time
}
class MultiMaterialJobState {
+string job_id
+Dict[int, str] file_ids
+int total_layers
+int current_layer
+Dict[int, List[LayerData]] remaining_layers
+Dict[int, List[str]] header_lines
+bool is_active
+JobStatus status
+float start_time
+float last_layer_time
+float estimated_completion
+bool waiting_for_print_start
+bool waiting_for_layer_complete
+string error_message
+int retry_count
+Dict[int, DrumState] drums
+get_progress_percentage() float
}
MultiMaterialJobState --> DrumState : "contains"
MultiMaterialJobState --> LayerData : "contains"
MultiMaterialJobState --> JobStatus : "references"
```

**Section sources**
- [multilayer_job.py](file://backend\app\models\multilayer_job.py#L19-L111) - *Internal domain model definitions*

## Bidirectional Data Mapping Architecture
The data mapping system uses a layered architecture to translate between internal application state and OPC UA variables. The `OPCUACoordinationMixin` provides a high-level interface for managing job state, while the OPC UA server handles the low-level protocol details. The `MultiMaterialJobService` orchestrates the complete workflow by combining `LayerProcessingMixin` for job lifecycle management and `OPCUACoordinationMixin` for variable management.

```mermaid
graph TD
subgraph "Application Layer"
A[FastAPI Endpoints]
B[Business Logic]
end
subgraph "Coordination Layer"
C[MultiMaterialJobService]
D[LayerProcessingMixin]
E[OPCUACoordinationMixin]
end
subgraph "Protocol Layer"
F[OPCUAService]
G[asyncua Server]
end
subgraph "Control System"
H[TwinCAT PLC]
end
A --> B
B --> C
C --> D
C --> E
E --> F
F --> G
G < --> H
style A fill:#f9f,stroke:#333
style B fill:#f9f,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#bbf,stroke:#333
style E fill:#bbf,stroke:#333
style F fill:#bbf,stroke:#333
style G fill:#9f9,stroke:#333
style H fill:#9f9,stroke:#333
```

**Section sources**
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L1-L332) - *Service orchestration*
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L250) - *OPC UA coordination logic*

## Writing Internal State to OPC UA Nodes
The process of writing internal state to OPC UA nodes involves type coercion, validation, and protocol-specific formatting. The `OPCUACoordinationMixin` provides high-level methods like `setup_opcua_job`, `update_opcua_layer_progress`, and `signal_opcua_layer_complete` that abstract the low-level details of variable updates.

```mermaid
flowchart TD
Start([Coordination Method Call]) --> ValidateState["Validate Job State"]
ValidateState --> StateValid{State Valid?}
StateValid --> |No| ReturnError["Return Error"]
StateValid --> |Yes| ResolveOPCUA["Resolve OPC UA Service"]
ResolveOPCUA --> OPCUAExists{OPC UA Available?}
OPCUAExists --> |No| ReturnError
OPCUAExists --> |Yes| CoerceValue["Coerce Value to OPC UA Type"]
CoerceValue --> WriteVariable["Write to OPC UA Variable"]
WriteVariable --> UpdateCache["Update Local Cache"]
UpdateCache --> TriggerHandlers["Trigger Change Handlers"]
TriggerHandlers --> ReturnSuccess["Return Success"]
style ReturnError fill:#f99,stroke:#333
style ReturnSuccess fill:#9f9,stroke:#333
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L100-L200) - *OPC UA update methods*

## Reading OPC UA Node Values into Internal State
Reading values from OPC UA nodes is now the primary source of truth for job status. The `get_job_status` method in `LayerProcessingMixin` asynchronously reads the 7 coordination variables and combines them with hardware state from the recoater client to compute the overall job status.

```mermaid
sequenceDiagram
participant Application as "Application"
participant LayerProcessing as "LayerProcessingMixin"
participant Coordination as "OPCUACoordinationMixin"
participant OPCUAServer as "OPCUAService"
Application->>LayerProcessing : get_job_status()
LayerProcessing->>OPCUAServer : read_variable("job_active")
LayerProcessing->>OPCUAServer : read_variable("total_layers")
LayerProcessing->>OPCUAServer : read_variable("current_layer")
LayerProcessing->>OPCUAServer : read_variable("recoater_ready_to_print")
LayerProcessing->>OPCUAServer : read_variable("recoater_layer_complete")
LayerProcessing->>OPCUAServer : read_variable("backend_error")
LayerProcessing->>OPCUAServer : read_variable("plc_error")
OPCUAServer-->>LayerProcessing : Return values
LayerProcessing->>LayerProcessing : Compute status from values
LayerProcessing->>LayerProcessing : Poll recoater hardware state
LayerProcessing-->>Application : Return status object
Note over LayerProcessing,OPCUAServer : Asynchronous OPC UA reads
Note over LayerProcessing,LayerProcessing : Status computation and hardware polling
```

**Section sources**
- [layer_operations_mixin.py](file://backend\app\services\job_management\mixins\layer_operations_mixin.py#L159-L193) - *Async get_job_status implementation*

## Type Conversion and Value Coercion
The system implements comprehensive type conversion to ensure data integrity when mapping between Python types and OPC UA data types. This includes handling of boolean, integer, string, and floating-point values with appropriate coercion rules. The `VariableDefinition` domain model specifies the expected data type for each variable.

```mermaid
flowchart TD
Input[Input Value] --> TypeCheck["Check Expected Type"]
TypeCheck --> Int32{"Expected: Int32?"}
Int32 --> |Yes| CoerceInt["Coerce to int"]
Int32 --> |No| Boolean{"Expected: Boolean?"}
Boolean --> |Yes| CoerceBool["Coerce to bool"]
Boolean --> |No| String{"Expected: String?"}
String --> |Yes| CoerceString["Coerce to str"]
String --> |No| Float{"Expected: Float?"}
Float --> |Yes| CoerceFloat["Coerce to float"]
Float --> |No| Double{"Expected: Double?"}
Double --> |Yes| CoerceDouble["Coerce to float"]
Double --> |No| Fallback["Use raw value"]
CoerceInt --> CreateVariant
CoerceBool --> CreateVariant
CoerceString --> CreateVariant
CoerceFloat --> CreateVariant
CoerceDouble --> CreateVariant
Fallback --> CreateVariant
CreateVariant["Create OPC UA Variant"] --> Output["Write to Node"]
style CreateVariant fill:#bbf,stroke:#333
style Output fill:#9f9,stroke:#333
```

**Section sources**
- [models.py](file://backend\app\services\opcua\models.py#L40-L64) - *VariableDefinition with data_type field*

## Error Handling and Null Value Management
The system implements robust error handling for both connection issues and data validation problems. The 7-variable architecture includes dedicated `backend_error` and `plc_error` flags that are used to signal error conditions. The `clear_all_error_flags` method in `MultiMaterialJobService` coordinates the clearing of both job-level and OPC UA-level error flags.

```mermaid
flowchart TD
Operation[Operation Attempt] --> Success{"Operation Successful?"}
Success --> |Yes| Complete["Complete Operation"]
Success --> |No| ExceptionType{"Exception Type?"}
ExceptionType --> Connection{"Connection Error?"}
Connection --> |Yes| HandleConnection["Log Warning, Return False"]
ExceptionType --> ValidationError{"Validation Error?"}
ValidationError --> |Yes| HandleValidation["Log Error, Return False"]
ExceptionType --> CoercionError{"Coercion Error?"}
CoercionError --> |Yes| HandleCoercion["Log Warning, Use Raw Value"]
ExceptionType --> Unknown{"Unknown Error?"}
Unknown --> |Yes| HandleUnknown["Log Error with Traceback"]
HandleConnection --> Return
HandleValidation --> Return
HandleCoercion --> Retry["Retry with Raw Value"]
Retry --> WriteRaw["Write Raw Value"]
WriteRaw --> Return
HandleUnknown --> Return
Return["Return Result"] --> End
style Complete fill:#9f9,stroke:#333
style Return fill:#f99,stroke:#333
style WriteRaw fill:#ff9,stroke:#333
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L50-L90) - *Error handling methods*
- [multimaterial_job_service.py](file://backend\app\services\job_management\multimaterial_job_service.py#L300-L332) - *clear_all_error_flags implementation*

## Coordination Engine and Status Polling
The coordination engine integrates with the status polling system to provide real-time updates to the frontend while maintaining synchronization with the PLC through OPC UA variables. The `wait_for_layer_completion` method in `OPCUACoordinationMixin` polls both OPC UA error flags and the recoater hardware state to determine when a layer has completed.

```mermaid
graph TD
subgraph "Backend"
A[StatusPollingService]
B[RecoaterDataGatherer]
C[WebSocketManager]
end
subgraph "Hardware"
D[Recoater Client]
end
subgraph "Frontend"
E[WebSocket Clients]
end
A --> B
B --> D
D --> B
B --> A
A --> C
C --> E
A -.->|Polls every 1s| B
B -.->|Gathers data| D
C -.->|Broadcasts| E
style A fill:#bbf,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#9f9,stroke:#333
style E fill:#f9f,stroke:#333
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L50-L90) - *wait_for_layer_completion implementation*

## Schema Diagrams and Field Mappings
The following diagram illustrates the complete mapping between the 7 coordination variables and their role in the system, showing the bidirectional data flow and transformation logic.

```mermaid
erDiagram
COORDINATION_VARIABLES {
string name PK
string node_id
string data_type
Any initial_value
bool writable
}
COORDINATION_VARIABLES {
"job_active" string
"total_layers" int
"current_layer" int
"recoater_ready_to_print" bool
"recoater_layer_complete" bool
"backend_error" bool
"plc_error" bool
}
note as VariableRoles
job_active: Backend sets TRUE at start, FALSE at end
total_layers: Backend sets once at job start
current_layer: Backend manages, PLC reads
recoater_ready_to_print: Backend writes when ready
recoater_layer_complete: Backend writes when deposition complete
backend_error: Backend writes on issues
plc_error: PLC writes on issues
end note
COORDINATION_VARIABLES .. VariableRoles
```

**Section sources**
- [coordination_mixin.py](file://backend\app\services\job_management\mixins\coordination_mixin.py#L1-L250) - *7-variable architecture documentation*
- [opcua_config.py](file://backend\app\config\opcua_config.py#L67-L107) - *Variable definitions*