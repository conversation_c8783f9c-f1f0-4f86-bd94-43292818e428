# OPC UA Configuration

<cite>
**Referenced Files in This Document**   
- [opcua_config.py](file://backend/app/config/opcua_config.py) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py) - *Updated in recent commit*
</cite>

## Update Summary
**Changes Made**   
- Updated documentation to reflect removal of redundant CoordinationStatus enum and OPC UA exceptions
- Simplified explanation of coordination variables structure
- Removed references to deprecated components
- Updated code examples to match current implementation
- Enhanced accuracy of configuration validation description

**New Sections Added**
- None

**Deprecated/Removed Sections**
- Removed section on CoordinationStatus enum
- Removed references to OPCUA exceptions module
- Removed redundant utility functions discussion

**Source Tracking System Updates and New Source Files**
- Updated file references to reflect current codebase structure
- Removed references to deleted exception files
- Maintained consistent English language throughout

## Table of Contents
1. [OPC UA Configuration Overview](#opc-ua-configuration-overview)
2. [Configuration Structure](#configuration-structure)
3. [Server Configuration](#server-configuration)
4. [Coordination Variables](#coordination-variables)
5. [Environment Variable Overrides](#environment-variable-overrides)
6. [OPCUA Server Service](#opcuaserver-service)
7. [Configuration Validation and Error Handling](#configuration-validation-and-error-handling)
8. [Best Practices](#best-practices)
9. [Common Configuration Errors](#common-configuration-errors)

## OPC UA Configuration Overview

The OPC UA configuration system in APIRecoater_Ethernet provides a centralized mechanism for managing all OPC UA connection parameters and coordination variables used in the multi-material print job system. The configuration is implemented in `opcua_config.py` and consumed by the OPCUAServer service to enable seamless communication between the FastAPI backend and the TwinCAT PLC.

The configuration system follows a hierarchical structure with two main components: server configuration parameters and coordination variables. This design enables the backend to host an OPC UA server that exposes specific variables for coordination with the PLC, creating a shared communication channel for job control, status monitoring, and error handling.

The configuration is designed to be flexible and environment-aware, supporting both default values and environment variable overrides. This allows the same codebase to be deployed across different environments (development, testing, production) with appropriate configuration settings without code changes.

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L292) - *Updated in recent commit*

## Configuration Structure

The OPC UA configuration system is structured around two primary data classes that define the server settings and coordination variables:

1. **OPCUAServerConfig**: Contains server-level configuration parameters
2. **CoordinationVariable**: Defines individual variables exposed by the OPC UA server

The configuration is centralized in the `opcua_config.py` file, which exports a global configuration instance and utility functions for accessing configuration data. This centralized approach ensures consistency across the application and simplifies maintenance.

The configuration structure supports both direct access to configuration objects and lookup functions for specific variables. This dual access pattern allows components to either work with the complete configuration or retrieve specific variables by name or type.

```python
@dataclass
class OPCUAServerConfig:
    """Configuration for OPC UA server hosting."""
    
    # Server endpoint configuration
    endpoint: str = "opc.tcp://0.0.0.0:4843/recoater/server/"
    server_name: str = "Recoater Multi-Material Coordination Server"
    
    # Namespace configuration
    namespace_uri: str = "http://recoater.backend.server"
    namespace_idx: int = 2
    
    # Security settings
    security_policy: str = "None"
    security_mode: str = "None"
    certificate_path: str = ""
    private_key_path: str = ""
    
    # Connection settings
    connection_timeout: float = 5.0
    session_timeout: float = 60.0
    
    # Server management
    auto_restart: bool = True
    restart_delay: float = 5.0
    max_restart_attempts: int = 3
```

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L101-L136) - *Updated in recent commit*

## Server Configuration

The server configuration parameters define how the OPC UA server is exposed to clients and how it manages connections and security. These settings are critical for ensuring reliable communication between the backend and the PLC.

### Endpoint Configuration
The server endpoint is specified in the standard OPC UA format: `opc.tcp://host:port/path`. The default configuration uses `opc.tcp://0.0.0.0:4843/recoater/server/`, which binds to all network interfaces on port 4843 with a specific path for the recoater service.

```mermaid
flowchart TD
A["OPC UA Client Connection"] --> B["Endpoint: opc.tcp://host:4843/recoater/server/"]
B --> C["Server binds to port 4843"]
C --> D["Path: /recoater/server/"]
D --> E["Client establishes connection"]
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L101-L104) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L228-L235) - *Updated in recent commit*

### Namespace Configuration
The configuration uses a custom namespace with URI `http://recoater.backend.server` and index 2. This namespace isolates the coordination variables from standard OPC UA namespaces and provides a unique identifier for the variables.

```python
# Namespace configuration
namespace_uri: str = "http://recoater.backend.server"
namespace_idx: int = 2
```

When variables are created, they use the namespace index in their node IDs (e.g., `ns=2;s=job_active`), ensuring they are properly scoped within the custom namespace.

### Security Configuration
The default security configuration is set to "None" for both security policy and mode, which is appropriate for internal networks where security is managed by network segmentation. For production environments requiring encryption, these can be overridden to use secure policies like "Basic256Sha256".

The configuration also supports certificate-based security by specifying paths to certificate and private key files, though these are empty by default.

### Connection and Session Timeouts
The configuration includes two timeout settings:
- **connection_timeout**: 5.0 seconds - The maximum time to establish a client connection
- **session_timeout**: 60.0 seconds - The maximum duration of an OPC UA session before it times out

These values balance responsiveness with stability, allowing sufficient time for connection establishment while preventing stale sessions from consuming resources.

### Server Management Settings
The configuration includes fault tolerance features:
- **auto_restart**: Enables automatic server restart on failure
- **restart_delay**: 5.0 seconds between restart attempts
- **max_restart_attempts**: 3 maximum restart attempts before giving up

These settings ensure the OPC UA server remains available even if it encounters transient errors.

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L101-L136) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L228-L264) - *Updated in recent commit*

## Coordination Variables

The coordination variables are the core communication mechanism between the backend and the PLC. These variables are defined in the `COORDINATION_VARIABLES` list and represent the shared state between the two systems.

### Variable Architecture
Variables use namespace-qualified node IDs in the format `ns=X;s=variable_name`, where:
- `ns=2`: References the custom namespace (namespace_idx=2)
- `s=variable_name`: String identifier for the variable

This naming convention ensures variables are uniquely identifiable within the OPC UA address space.

```python
@dataclass
class CoordinationVariable:
    """Definition for a coordination variable hosted by OPC UA server."""
    
    name: str
    node_id: str
    data_type: str
    initial_value: Any
    writable: bool = True
    description: str = ""
```

### Job Control Variables
These variables manage the overall print job state:

```python
CoordinationVariable(
    name="job_active",
    node_id="ns=2;s=job_active",
    data_type="Boolean",
    initial_value=False,
    description="Backend sets TRUE at start, FALSE at end"
),
CoordinationVariable(
    name="total_layers",
    node_id="ns=2;s=total_layers",
    data_type="Int32",
    initial_value=0,
    description="Backend sets once at job start"
),
CoordinationVariable(
    name="current_layer",
    node_id="ns=2;s=current_layer",
    data_type="Int32",
    initial_value=0,
    description="Backend manages, PLC reads"
)
```

### Recoater Coordination Variables
These variables coordinate the recoater operations:

```python
CoordinationVariable(
    name="recoater_ready_to_print",
    node_id="ns=2;s=recoater_ready_to_print",
    data_type="Boolean",
    initial_value=False,
    description="Backend writes when Aerosint is ready"
),
CoordinationVariable(
    name="recoater_layer_complete",
    node_id="ns=2;s=recoater_layer_complete",
    data_type="Boolean",
    initial_value=False,
    description="Backend writes when deposition complete"
)
```

### Error Handling Variables
These variables provide bidirectional error reporting:

```python
CoordinationVariable(
    name="backend_error",
    node_id="ns=2;s=backend_error",
    data_type="Boolean",
    initial_value=False,
    description="Backend writes if any issue arises"
),
CoordinationVariable(
    name="plc_error",
    node_id="ns=2;s=plc_error",
    data_type="Boolean",
    initial_value=False,
    description="PLC writes if any issues"
)
```

All variables are created in a dedicated "RecoaterCoordination" folder within the OPC UA address space, providing logical organization.

```mermaid
graph TD
A["OPC UA Server"] --> B["Objects Folder"]
B --> C["RecoaterCoordination Folder"]
C --> D["job_active: Boolean"]
C --> E["total_layers: Int32"]
C --> F["current_layer: Int32"]
C --> G["recoater_ready_to_print: Boolean"]
C --> H["recoater_layer_complete: Boolean"]
C --> I["backend_error: Boolean"]
C --> J["plc_error: Boolean"]
```

**Diagram sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L151-L173) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L326-L357) - *Updated in recent commit*

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L151-L173) - *Updated in recent commit*

## Environment Variable Overrides

The configuration system supports environment variable overrides, allowing deployment-specific settings without code changes. This is implemented through the `get_opcua_config()` function, which reads environment variables and falls back to defaults if they are not set.

```python
def get_opcua_config() -> OPCUAServerConfig:
    """
    Get OPC UA server configuration from environment variables or defaults.
    
    Returns:
        OPCUAServerConfig: Configuration object with server settings
    """
    return OPCUAServerConfig(
        endpoint=os.getenv("OPCUA_SERVER_ENDPOINT", "opc.tcp://0.0.0.0:4843/recoater/server/"),
        server_name=os.getenv("OPCUA_SERVER_NAME", "Recoater Multi-Material Coordination Server"),
        namespace_uri=os.getenv("OPCUA_NAMESPACE_URI", "http://recoater.backend.server"),
        namespace_idx=int(os.getenv("OPCUA_NAMESPACE_IDX", "2")),
        security_policy=os.getenv("OPCUA_SECURITY_POLICY", "None"),
        security_mode=os.getenv("OPCUA_SECURITY_MODE", "None"),
        certificate_path=os.getenv("OPCUA_CERTIFICATE_PATH", ""),
        private_key_path=os.getenv("OPCUA_PRIVATE_KEY_PATH", ""),
        connection_timeout=float(os.getenv("OPCUA_CONNECTION_TIMEOUT", "5.0")),
        session_timeout=float(os.getenv("OPCUA_SESSION_TIMEOUT", "60.0")),
        auto_restart=os.getenv("OPCUA_AUTO_RESTART", "true").lower() == "true",
        restart_delay=float(os.getenv("OPCUA_RESTART_DELAY", "5.0")),
        max_restart_attempts=int(os.getenv("OPCUA_MAX_RESTART_ATTEMPTS", "3"))
    )
```

The following environment variables can be used to override configuration settings:

**Server Configuration:**
- **OPCUA_SERVER_ENDPOINT**: Server endpoint URL
- **OPCUA_SERVER_NAME**: Server display name
- **OPCUA_NAMESPACE_URI**: Namespace identifier
- **OPCUA_NAMESPACE_IDX**: Namespace numeric index

**Security Configuration:**
- **OPCUA_SECURITY_POLICY**: Security policy (None, Basic256Sha256, etc.)
- **OPCUA_SECURITY_MODE**: Security mode (None, Sign, SignAndEncrypt)
- **OPCUA_CERTIFICATE_PATH**: Path to server certificate file
- **OPCUA_PRIVATE_KEY_PATH**: Path to private key file

**Connection Configuration:**
- **OPCUA_CONNECTION_TIMEOUT**: Client connection timeout (seconds)
- **OPCUA_SESSION_TIMEOUT**: OPC UA session timeout (seconds)

**Server Management:**
- **OPCUA_AUTO_RESTART**: Enable automatic restart (true/false)
- **OPCUA_RESTART_DELAY**: Delay between restart attempts (seconds)
- **OPCUA_MAX_RESTART_ATTEMPTS**: Maximum restart attempts

This environment-based configuration approach enables flexible deployment across different environments while maintaining a single codebase.

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L235-L256) - *Updated in recent commit*

## OPCUA Server Service

The OPCUAServerManager service implements the low-level OPC UA server functionality, managing the creation and operation of the OPC UA server instance.

### Server Lifecycle
The server manager handles the complete server lifecycle through `start_server()` and `stop_server()` methods:

```python
async def start_server(self) -> bool:
    """
    Start the OPC UA server and initialize variables.

    Returns:
        bool: True if server started successfully
    """
    try:
        if self._running:
            logger.warning("OPC UA server is already running")
            return True

        # Initialize server with minimal setup
        self.server = Server()
        await self.server.init()

        # Configure server
        self.server.set_endpoint(self.config.endpoint)
        self.server.set_server_name(self.config.server_name)

        # Register namespace
        self.namespace_idx = await self.server.register_namespace(self.config.namespace_uri)
        logger.info(f"Registered namespace '{self.config.namespace_uri}' with index {self.namespace_idx}")

        # Create coordination variables
        await self._create_coordination_variables()

        # Start server
        await self.server.start()
        self._running = True
        self._restart_count = 0
        logger.info(f"OPC UA server started successfully on {self.config.endpoint}")

        # Start heartbeat task
        self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())

        return True

    except Exception as e:
        logger.error(f"Failed to start OPC UA server: {e}")
        await self._handle_server_error(e)
        return False
```

### Variable Creation
The server manager creates all coordination variables during startup:

```python
async def _create_coordination_variables(self) -> None:
    """Create all coordination variables in the server address space."""
    try:
        logger.info("Starting coordination variables creation...")

        # Use the cleaner approach from the simulator
        objects = self.server.nodes.objects
        logger.debug("Retrieved objects node")

        # Create a coordination folder first
        coordination_folder = await objects.add_folder(
            self.namespace_idx,
            "RecoaterCoordination"
        )
        logger.debug("Created RecoaterCoordination folder")

        # Create variables in the folder
        created_count = 0
        for var_def in COORDINATION_VARIABLES:
            try:
                logger.debug(f"Creating variable: {var_def.name}")

                # Determine OPC UA data type
                ua_type = self._get_ua_data_type(var_def.data_type)
                logger.debug(f"Variable {var_def.name} - UA type: {ua_type}")

                # Create variable node in coordination folder
                var_node = await coordination_folder.add_variable(
                    self.namespace_idx,
                    var_def.name,
                    var_def.initial_value,
                    ua_type
                )
                logger.debug(f"Variable {var_def.name} - node created")

                # Set writable if specified
                if var_def.writable:
                    try:
                        await var_node.set_writable()
                        logger.debug(f"Variable {var_def.name} - set as writable")
                    except Exception as writable_error:
                        logger.warning(f"Failed to set writable for {var_def.name}: {writable_error}")

                # Store reference
                self.variable_nodes[var_def.name] = var_node
                created_count += 1

                logger.info(f"Successfully created variable: {var_def.name} = {var_def.initial_value}")

            except Exception as e:
                logger.error(f"Failed to create variable {var_def.name}: {e}")
                # Continue with other variables instead of failing completely
                continue

        logger.info(f"Created {created_count} out of {len(COORDINATION_VARIABLES)} coordination variables")

        if created_count == 0:
            raise RuntimeError("Failed to create any coordination variables")

    except Exception as e:
        logger.error(f"Failed to create coordination variables: {e}")
        raise
```

### Data Type Handling
The server manager includes robust data type handling to ensure type safety:

```python
def _get_ua_data_type(self, data_type: str) -> ua.VariantType:
    """
    Convert string data type to OPC UA VariantType.
    
    Args:
        data_type: String representation of data type
        
    Returns:
        ua.VariantType: OPC UA variant type
    """
    type_mapping = {
        "Boolean": ua.VariantType.Boolean,
        "String": ua.VariantType.String,
        "Int32": ua.VariantType.Int32,
        "Float": ua.VariantType.Float,
        "Double": ua.VariantType.Double,
        "DateTime": ua.VariantType.DateTime
    }
    
    return type_mapping.get(data_type, ua.VariantType.String)
```

### Fault Tolerance
The server manager implements fault tolerance through automatic restart capabilities:

```python
async def _handle_server_error(self, error: Exception) -> None:
    """
    Handle server errors with auto-restart if configured.
    
    Args:
        error: Exception that occurred
    """
    logger.error(f"OPC UA server error: {error}")
    
    if self.config.auto_restart and self._restart_count < self.config.max_restart_attempts:
        self._restart_count += 1
        logger.info(f"Attempting server restart ({self._restart_count}/{self.config.max_restart_attempts})")
        
        await asyncio.sleep(self.config.restart_delay)
        
        # Attempt restart
        try:
            await self.start_server()
        except Exception as restart_error:
            logger.error(f"Server restart failed: {restart_error}")
    else:
        logger.error("Max restart attempts reached or auto-restart disabled")
```

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L1-L525) - *Updated in recent commit*

## Configuration Validation and Error Handling

The configuration system includes comprehensive validation and error handling to ensure reliability and provide meaningful feedback when issues occur.

### Configuration Validation
The system validates configuration at multiple levels:

1. **Environment variable parsing**: String values are converted to appropriate types with default fallbacks
2. **Server startup validation**: The server checks its running state before starting
3. **Variable creation validation**: Each variable is created with appropriate type checking
4. **Write operation validation**: Data types are validated before writing to variables

```python
async def write_variable(self, name: str, value: Any) -> bool:
    """
    Write value to a coordination variable.
    
    Args:
        name: Variable name
        value: Value to write
        
    Returns:
        bool: True if write successful
    """
    try:
        if not self._running:
            logger.warning(f"Cannot write variable {name}: server not running")
            return False
        
        if name not in self.variable_nodes:
            logger.error(f"Variable {name} not found")
            return False
        
        node = self.variable_nodes[name]

        # Determine expected data type from configuration to avoid BadTypeMismatch
        expected_type = None
        for var_def in COORDINATION_VARIABLES:
            if var_def.name == name:
                expected_type = var_def.data_type
                break

        coerced_value = value
        ua_variant = None

        try:
            if expected_type == "Int32" and isinstance(value, (int, bool)):
                # bool is subclass of int; ensure pure int for Int32
                coerced_value = int(value)
                ua_variant = ua.Variant(coerced_value, ua.VariantType.Int32)
            elif expected_type == "Boolean":
                coerced_value = bool(value)
                ua_variant = ua.Variant(coerced_value, ua.VariantType.Boolean)
            elif expected_type == "String":
                coerced_value = "" if value is None else str(value)
                ua_variant = ua.Variant(coerced_value, ua.VariantType.String)
            elif expected_type == "Float":
                coerced_value = float(value)
                ua_variant = ua.Variant(coerced_value, ua.VariantType.Float)
            elif expected_type == "Double":
                coerced_value = float(value)
                ua_variant = ua.Variant(coerced_value, ua.VariantType.Double)
            else:
                # Fallback: let asyncua infer type
                ua_variant = None
        except Exception as coerce_err:
            logger.warning(f"Failed to coerce value for {name}: {coerce_err}; using raw value")
            ua_variant = None

        if ua_variant is not None:
            await node.write_value(ua_variant)
        else:
            await node.write_value(coerced_value)
        
        logger.debug(f"Updated variable {name} = {coerced_value} (expected_type={expected_type})")
        return True
        
    except Exception as e:
        logger.error(f"Failed to write variable {name}: {e}")
        return False
```

### Error Handling Strategies
The system employs several error handling strategies:

1. **Graceful degradation**: When a variable cannot be created, the system continues with other variables
2. **Automatic recovery**: The server attempts to restart on failure
3. **Comprehensive logging**: All errors are logged with context for debugging
4. **State consistency**: The system maintains consistent state even when errors occur

### Validation Best Practices
To ensure configuration integrity before service startup:

1. Validate all environment variables at startup
2. Test connectivity to the OPC UA server endpoint
3. Verify that all required variables are created
4. Check that the server is responsive to read/write operations
5. Validate data types of all variables

```python
def get_server_status(self) -> Dict[str, Any]:
    """Get server status information."""
    return {
        "connected": self._connected,
        "server_running": self.server_manager.is_running,
        "endpoint": self.config.endpoint,
        "namespace": self.config.namespace_uri,
        "variable_count": len(self.server_manager.get_variable_names())
    }
```

This status method provides a comprehensive view of the server state, which can be used for validation and monitoring.

**Section sources**
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L393-L420) - *Updated in recent commit*

## Best Practices

### Configuration Management
1. **Use environment variables for deployment-specific settings**: Keep sensitive information and environment-specific values in environment variables rather than hardcoding them
2. **Validate configuration at startup**: Implement comprehensive validation to catch configuration errors early
3. **Provide meaningful defaults**: Ensure the system works with default settings in development environments
4. **Document all configuration options**: Maintain up-to-date documentation of all configuration parameters and their valid values

### Security
1. **Use secure policies in production**: Enable appropriate security policies and modes for production deployments
2. **Protect certificate files**: Ensure certificate and private key files have appropriate file permissions
3. **Use network segmentation**: Isolate the OPC UA server on a dedicated network segment
4. **Implement monitoring**: Monitor for unauthorized access attempts and anomalous behavior

### Performance
1. **Optimize polling intervals**: Balance between responsiveness and network load
2. **Use subscriptions when possible**: For real-time updates, use OPC UA subscriptions rather than polling
3. **Monitor server resources**: Track memory and CPU usage to ensure the server remains responsive
4. **Implement connection pooling**: Reuse connections when possible to reduce overhead

### Reliability
1. **Enable automatic restart**: Use the auto-restart feature to recover from transient failures
2. **Implement health checks**: Provide endpoints to check the health of the OPC UA server
3. **Use redundant systems**: For critical applications, consider implementing redundant OPC UA servers
4. **Monitor connection status**: Continuously monitor the connection status between backend and PLC

### Development and Testing
1. **Use the OPC UA simulator**: Test with the provided `opcua_simulator.py` before connecting to real hardware
2. **Implement comprehensive tests**: Use the test suite to validate configuration changes
3. **Test error scenarios**: Verify that the system handles network failures and other error conditions gracefully
4. **Document configuration changes**: Maintain a changelog of configuration modifications

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L1-L292) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L1-L525) - *Updated in recent commit*

## Common Configuration Errors

### Incorrect Node IDs
One of the most common errors is using incorrect node IDs when accessing variables. The node ID format must be exactly `ns=X;s=variable_name` with the correct namespace index.

**Error Example:**
```python
# Incorrect - wrong namespace index
node_id = "ns=1;s=job_active"

# Correct - matches configuration
node_id = "ns=2;s=job_active"
```

**Solution:** Always use the `get_variable_by_name()` function to retrieve the correct node ID:

```python
from app.config.opcua_config import get_variable_by_name

var_def = get_variable_by_name("job_active")
node_id = var_def.node_id  # Guaranteed to be correct
```

### Mismatched Data Types
Writing values with incorrect data types can cause `BadTypeMismatch` errors.

**Error Example:**
```python
# Incorrect - writing string to Int32 variable
await server_manager.write_variable("total_layers", "100")

# Correct - writing integer to Int32 variable
await server_manager.write_variable("total_layers", 100)
```

**Solution:** The server manager automatically coerces values to the expected type, but it's best to ensure the correct type is used:

```python
# Safe approach - let the server handle coercion
await server_manager.write_variable("total_layers", "100")  # Will be coerced to int

# Preferred approach - use correct type
await server_manager.write_variable("total_layers", 100)
```

### Network Unreachable Settings
Incorrect endpoint configuration can prevent the server from starting or clients from connecting.

**Common Issues:**
- Using `localhost` or `127.0.0.1` when clients are on different machines
- Firewall blocking the OPC UA port (4843)
- Incorrect port number in the endpoint URL
- Network interface binding issues

**Solutions:**
1. Use `0.0.0.0` to bind to all interfaces:
   ```python
   endpoint = "opc.tcp://0.0.0.0:4843/recoater/server/"
   ```
2. Ensure the firewall allows traffic on port 4843
3. Verify network connectivity between client and server
4. Test with the `simple_connection_test.py` script

### Security Configuration Errors
Misconfigured security settings can prevent connections.

**Common Issues:**
- Mismatched security policies between client and server
- Missing certificate files
- Incorrect file paths to certificates
- Certificate expiration

**Solutions:**
1. Ensure client and server use compatible security policies
2. Verify certificate files exist at specified paths
3. Check file permissions on certificate files
4. Use `None` security for testing, then enable appropriate security for production

### Namespace Configuration Errors
Incorrect namespace configuration can make variables inaccessible.

**Common Issues:**
- Namespace URI mismatch
- Namespace index mismatch
- Namespace not properly registered

**Solutions:**
1. Verify the namespace URI and index match between configuration and client:
   ```python
   namespace_uri = "http://recoater.backend.server"
   namespace_idx = 2
   ```
2. Check server logs for namespace registration success
3. Use the `get_server_status()` method to verify the namespace is registered

### Variable Writable Configuration
By default, all variables are writable, but this can be overridden.

**Error Example:**
```python
# Variable defined as read-only
CoordinationVariable(
    name="current_layer",
    node_id="ns=2;s=current_layer",
    data_type="Int32",
    initial_value=0,
    writable=False,  # Cannot be written by PLC
    description="Backend manages, PLC reads"
)
```

**Solution:** Ensure the `writable` flag is set appropriately for each variable based on the intended access pattern.

**Section sources**
- [opcua_config.py](file://backend/app/config/opcua_config.py#L151-L173) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L326-L357) - *Updated in recent commit*
- [opcua_server.py](file://backend/app/services/opcua/opcua_service.py#L393-L420) - *Updated in recent commit*