# OPC UA Simulator and Testing Tools

<cite>
**Referenced Files in This Document**   
- [opcua_simulator.py](file://tests\opcua_simulator.py)
- [test_opcua_client.py](file://tests\test_opcua_client.py)
- [backend\tests\mock_opcua_client.py](file://backend\tests\mock_opcua_client.py)
- [backend\app\services\opcua\opcua_service.py](file://backend\app\services\opcua\opcua_service.py)
- [backend\app\services\opcua\mixins\coordination_mixin.py](file://backend\app\services\opcua\mixins\coordination_mixin.py)
- [backend\app\config\opcua_config.py](file://backend\app\config\opcua_config.py)
- [backend\tests\business_logic\services\test_opcua_coordination_mixin.py](file://backend\tests\business_logic\services\test_opcua_coordination_mixin.py) - *Updated in recent commit*
</cite>

## Update Summary
**Changes Made**   
- Updated Testing and Validation Tools section to reflect new test structure and removed deprecated methods
- Added new content about unit test implementation using coordination mixin
- Removed references to deprecated `get_multimaterial_status` method
- Updated section sources to include newly relevant test files
- Enhanced documentation of test assertions and validation patterns

## Table of Contents
1. [Introduction](#introduction)
2. [OPC UA Simulation Infrastructure](#opc-ua-simulation-infrastructure)
3. [OPC UA Coordinator Architecture](#opc-ua-coordinator-architecture)
4. [Testing and Validation Tools](#testing-and-validation-tools)
5. [Integration and Usage Patterns](#integration-and-usage-patterns)
6. [Configuration and Setup](#configuration-and-setup)
7. [Troubleshooting Guide](#troubleshooting-guide)

## Introduction
This document provides comprehensive documentation for the OPC UA simulation and testing infrastructure within the recoater system. The architecture enables local development and integration testing through a simulated OPC UA server that mimics the behavior of the actual recoater device. The system comprises three key components: `opcua_simulator.py` which provides a standalone OPC UA server, `test_opcua_client.py` which validates connectivity and command execution, and `mock_opcua_client.py` which enables unit testing by stubbing OPC UA interactions. This infrastructure allows developers to test backend-PLC coordination logic without requiring physical hardware, supporting both integration testing and automated unit tests under various network conditions and fault scenarios.

## OPC UA Simulation Infrastructure

The OPC UA simulation infrastructure provides a realistic environment for testing OPC UA client-server interactions without requiring physical hardware. The core component is the `OPCUAMachineSimulator` class in `opcua_simulator.py`, which uses the FreeOpcUa library to create a fully functional OPC UA server that exposes mock nodes for industrial machine parameters.

```mermaid
classDiagram
class OPCUAMachineSimulator {
+string machine_type
+int port
+int namespace_index
+Server server
+dict nodes
+bool running
-__init__(machine_type, port, namespace_index)
+start_server() bool
+stop_server() bool
-_create_machine_nodes(namespace_idx) void
-_create_3d_printer_nodes(parent_folder, namespace_idx) void
-_create_cnc_machine_nodes(parent_folder, namespace_idx) void
-_create_power_meter_nodes(parent_folder, namespace_idx) void
-_simulate_machine_data() void
-_update_3d_printer_data(time_step) void
-_update_cnc_machine_data(time_step) void
-_update_power_meter_data(time_step) void
}
OPCUAMachineSimulator --> Server : "uses"
OPCUAMachineSimulator --> ua : "uses"
```

**Diagram sources**
- [opcua_simulator.py](file://tests\opcua_simulator.py#L44-L375)

**Section sources**
- [opcua_simulator.py](file://tests\opcua_simulator.py#L0-L375)

The simulator supports multiple machine types including 3D printers, CNC machines, and power meters, each with specific node structures. For a 3D printer simulation, it creates nodes for temperature, pressure, humidity, build progress, and status. The simulator continuously updates these values with realistic patterns, such as sine wave variations for temperature and incremental progress for build completion. The simulation runs asynchronously using asyncio, updating values every second to mimic real-world machine behavior.

The simulator is configured through a YAML configuration file that specifies which machine types to simulate and their respective ports. The `start_multiple_simulators` function reads this configuration and launches multiple simulator instances simultaneously, allowing for complex test scenarios involving multiple devices. Each simulator instance runs on a separate port and creates a distinct namespace, preventing naming conflicts and enabling independent testing of different machine types.

## OPC UA Coordinator Architecture

The OPC UA coordinator architecture provides a high-level interface for managing OPC UA communications between the backend application and the PLC. The `OPCUACoordinator` class in `opcua_coordinator.py` serves as the primary interface, abstracting away the complexities of direct OPC UA protocol interactions.

```mermaid
graph TD
subgraph "Backend Application"
A[FastAPI Endpoints]
B[OPCUACoordinator]
C[OPCUAServerManager]
end
subgraph "OPC UA Protocol"
D[asyncua.Server]
end
subgraph "External Systems"
E[TwinCAT PLC]
end
A --> B
B --> C
C --> D
D --> E
style A fill:#f9f,stroke:#333
style B fill:#bbf,stroke:#333
style C fill:#bbf,stroke:#333
style D fill:#9f9,stroke:#333
style E fill:#f96,stroke:#333
```

**Diagram sources**
- [backend\app\services\opcua\opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L50-L59)
- [backend\app\services\opcua\mixins\server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py#L29-L43)

**Section sources**
- [backend\app\services\opcua\opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L0-L589)
- [backend\app\services\opcua\mixins\server_mixin.py](file://backend\app\services\opcua\mixins\server_mixin.py#L0-L524)

The coordinator operates on a layered architecture with three distinct levels of abstraction:

1. **Application Layer**: FastAPI endpoints that handle HTTP requests from the frontend
2. **Business Logic Layer**: The `OPCUACoordinator` class that provides high-level methods for job management
3. **Protocol Layer**: The `OPCUAServerManager` class that handles low-level OPC UA protocol operations

The coordinator manages seven key coordination variables that facilitate communication between the backend and PLC:
- Job control variables: `job_active`, `total_layers`, `current_layer`
- Recoater coordination variables: `recoater_ready_to_print`, `recoater_layer_complete`
- Error handling variables: `backend_error`, `plc_error`

These variables serve as shared "mailboxes" for coordination, enabling real-time communication without polling delays. The coordinator provides both high-level convenience methods (e.g., `set_job_active()`) and low-level access methods (e.g., `write_variable()`) to accommodate different usage patterns.

## Testing and Validation Tools

The testing and validation tools provide comprehensive coverage for both integration testing and unit testing of OPC UA functionality. The infrastructure includes two primary testing components: `test_opcua_client.py` for integration testing and `mock_opcua_client.py` for unit testing.

```mermaid
sequenceDiagram
participant TestClient as "test_opcua_client.py"
participant Simulator as "opcua_simulator.py"
participant Coordinator as "opcua_coordinator.py"
TestClient->>Simulator : connect()
Simulator-->>TestClient : Connection established
TestClient->>Simulator : browse_node()
Simulator-->>TestClient : Node structure
TestClient->>Simulator : find_coordination_variables()
Simulator-->>TestClient : Variable values
TestClient->>Simulator : write_variable()
Simulator-->>TestClient : Write confirmation
TestClient->>Simulator : read_variable()
Simulator-->>TestClient : Updated value
TestClient->>Simulator : disconnect()
```

**Diagram sources**
- [test_opcua_client.py](file://tests\test_opcua_client.py#L0-L261)
- [opcua_simulator.py](file://tests\opcua_simulator.py#L0-L375)

The integration test client in `test_opcua_client.py` validates connectivity and command execution against the simulator. It performs several key validation steps:
- Connects to the OPC UA server and verifies the connection
- Retrieves server information including namespace array and server array
- Browses the node structure to discover available variables
- Finds and reads coordination variables
- Tests write operations on writable variables
- Verifies that written values can be read back correctly

For unit testing, the `MockOPCUAClient` class in `mock_opcua_client.py` provides a complete mock implementation that can simulate PLC behavior. This allows testing of the coordinator's logic without requiring a running OPC UA server. The mock client supports:
- Connection and disconnection simulation
- Variable reading and writing
- Subscription to variable changes
- Simulation of PLC behavior patterns
- Discovery of coordination variables

The mock client can simulate typical PLC behavior during a print job, including waiting for all drums to be ready, signaling print start and completion, and handling layer processing cycles. This enables comprehensive testing of the coordinator's state management and error handling capabilities under various scenarios.

The updated test suite in `test_opcua_coordination_mixin.py` demonstrates how to validate expected variable changes and method calls during automated tests. Test cases verify:
- Job setup initializes `job_active`, `total_layers`, and `current_layer` correctly
- Layer progress updates are reflected in `current_layer` variable
- Ready and complete signals are properly set and reset
- Error conditions (both backend and PLC) are detected and handled
- Exception handling during state polling

```python
async def test_setup_reset_signal_cleanup(opcua_service: OPCUAService):
    rc = Mock()
    d = DummyCoordinator(rc, opcua_service, poll_interval=0.0)

    await d.setup_opcua_job(5)
    assert await opcua_service.read_variable("job_active") is True
    assert await opcua_service.read_variable("total_layers") == 5
    assert await opcua_service.read_variable("current_layer") == 1

    await d.reset_opcua_layer_flags()
    assert await opcua_service.read_variable("recoater_ready_to_print") is False
    assert await opcua_service.read_variable("recoater_layer_complete") is False

    await d.signal_opcua_ready_to_print()
    assert await opcua_service.read_variable("recoater_ready_to_print") is True

    await d.signal_opcua_layer_complete()
    assert await opcua_service.read_variable("recoater_layer_complete") is True

    await d.update_opcua_layer_progress(3)
    assert await opcua_service.read_variable("current_layer") == 3

    await d.cleanup_opcua_job()
    assert await opcua_service.read_variable("job_active") is False
```

**Section sources**
- [test_opcua_client.py](file://tests\test_opcua_client.py#L0-L261)
- [backend\tests\mock_opcua_client.py](file://backend\tests\mock_opcua_client.py#L0-L382)
- [backend\tests\business_logic\services\test_opcua_coordination_mixin.py](file://backend\tests\business_logic\services\test_opcua_coordination_mixin.py#L0-L115) - *Updated in recent commit*

## Integration and Usage Patterns

The integration and usage patterns demonstrate how the various components work together to support development, testing, and production scenarios. The architecture supports multiple usage patterns that accommodate different development and testing requirements.

For local development and integration testing, developers can run the OPC UA simulator alongside the main application. The simulator creates a realistic environment where the backend can interact with what appears to be a real PLC. This allows for end-to-end testing of the entire coordination workflow without requiring physical hardware.

```mermaid
flowchart TD
Start([Start Development Environment]) --> LaunchSimulator["Launch opcua_simulator.py"]
LaunchSimulator --> LaunchBackend["Launch Backend Application"]
LaunchBackend --> ConnectBackend["Backend connects to OPC UA simulator"]
ConnectBackend --> TestAPI["Test API endpoints"]
TestAPI --> Validate["Validate coordinator behavior"]
Validate --> End([Development Complete])
style Start fill:#f9f,stroke:#333
style End fill:#f9f,stroke:#333
```

For unit testing, the mock OPC UA client is used to stub interactions with the coordinator. This allows for isolated testing of the coordinator's logic without dependencies on network connectivity or external services. Test cases can verify that:
- Expected variable changes occur when coordinator methods are called
- Method calls trigger the correct sequence of operations
- Error conditions are handled appropriately
- Event handlers are properly registered and invoked

The coordinator's design supports dependency injection, allowing the mock client to be substituted for the real server manager during testing. This enables comprehensive test coverage of both success and failure scenarios, including network failures, invalid data, and edge cases.

**Section sources**
- [backend\app\services\opcua\opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L0-L589)
- [backend\tests\mock_opcua_client.py](file://backend\tests\mock_opcua_client.py#L0-L382)
- [test_opcua_client.py](file://tests\test_opcua_client.py#L0-L261)

## Configuration and Setup

The configuration and setup process for the OPC UA simulation and testing infrastructure is designed to be straightforward and flexible. The system uses a combination of configuration files and environment variables to control behavior across different environments.

The primary configuration is defined in `opcua_config.py`, which contains the `OPCUAServerConfig` class and the `COORDINATION_VARIABLES` list. The configuration includes:
- Server endpoint and connection settings
- Namespace configuration
- Security settings
- Connection and session timeouts
- Auto-restart policies

```mermaid
erDiagram
OPCUAServerConfig {
string endpoint PK
string server_name
string namespace_uri
int namespace_idx
string security_policy
string security_mode
string certificate_path
string private_key_path
float connection_timeout
float session_timeout
bool auto_restart
float restart_delay
int max_restart_attempts
}
CoordinationVariable {
string name PK
string node_id
string data_type
any initial_value
bool writable
string description
}
OPCUAServerConfig ||--o{ CoordinationVariable : "contains"
```

**Diagram sources**
- [backend\app\config\opcua_config.py](file://backend\app\config\opcua_config.py#L0-L293)

All configuration values can be overridden via environment variables, allowing for easy customization across different deployment environments. For example, the server endpoint can be changed using the `OPCUA_SERVER_ENDPOINT` environment variable, and security settings can be adjusted using `OPCUA_SECURITY_POLICY` and `OPCUA_SECURITY_MODE`.

To set up the simulation environment:
1. Install the required dependencies using `install_deps.bat`
2. Run the OPC UA simulator using `opcua_simulator.py`
3. Start the backend application using `run.bat`
4. Validate connectivity using `test_opcua_client.py`

The simulator can be configured to inject faults by modifying the simulation logic in `opcua_simulator.py`. For example, the `_update_3d_printer_data` method can be modified to introduce abnormal temperature readings or stalled build progress to test error handling.

**Section sources**
- [backend\app\config\opcua_config.py](file://backend\app\config\opcua_config.py#L0-L293)
- [opcua_simulator.py](file://tests\opcua_simulator.py#L0-L375)

## Troubleshooting Guide

The troubleshooting guide provides solutions for common issues encountered when working with the OPC UA simulation and testing infrastructure. The most common issues relate to connectivity, configuration, and simulation behavior.

**Connection Issues**
- **Problem**: Unable to connect to OPC UA server
- **Solution**: Verify that the simulator is running and check the port number in the configuration. Ensure that no firewall is blocking the connection.

**Configuration Issues**
- **Problem**: Variables not appearing as expected
- **Solution**: Check the `COORDINATION_VARIABLES` list in `opcua_config.py` and verify that the namespace index matches across components.

**Simulation Issues**
- **Problem**: Simulation data not updating
- **Solution**: Verify that the asyncio event loop is running and check for errors in the simulation loop.

**Testing Issues**
- **Problem**: Mock client not behaving as expected
- **Solution**: Ensure that the mock client is properly initialized with the correct endpoint and that variable discovery is working.

The system includes comprehensive logging that can be used to diagnose issues. The log level can be adjusted by modifying the logging configuration in each component. For detailed debugging, set the log level to DEBUG to see all OPC UA operations and state changes.

**Section sources**
- [opcua_simulator.py](file://tests\opcua_simulator.py#L0-L375)
- [test_opcua_client.py](file://tests\test_opcua_client.py#L0-L261)
- [backend\tests\mock_opcua_client.py](file://backend\tests\mock_opcua_client.py#L0-L382)
- [backend\app\services\opcua\opcua_service.py](file://backend\app\services\opcua\opcua_service.py#L0-L589)